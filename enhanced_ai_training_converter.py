"""
Enhanced AI Training Data Converter
Converts your natural language observations into robust AI training format
with specific suggestions for improvement
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List
import numpy as np

class EnhancedAITrainingConverter:
    def __init__(self, csv_path: str = "Nifty.csv"):
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        
    def create_enhanced_observation_format(self, date: str, your_analysis: str, 
                                         validation_results: Dict) -> Dict:
        """Create enhanced observation format for AI training"""
        
        day_data = self.df[self.df['datetime'].dt.date == pd.to_datetime(date).date()]
        
        enhanced_obs = {
            'observation_metadata': {
                'observation_id': f"{date.replace('-', '_')}_ENHANCED",
                'date': date,
                'analyst': 'Your Personal Analysis',
                'created_at': datetime.now().isoformat(),
                'validation_score': self.calculate_validation_score(validation_results)
            },
            
            'raw_analysis': {
                'original_text': your_analysis,
                'word_count': len(your_analysis.split()),
                'key_phrases': self.extract_key_phrases(your_analysis)
            },
            
            'market_data_context': {
                'day_open': day_data.iloc[0]['open'] if not day_data.empty else None,
                'day_high': day_data['high'].max() if not day_data.empty else None,
                'day_low': day_data['low'].min() if not day_data.empty else None,
                'day_close': day_data.iloc[-1]['close'] if not day_data.empty else None,
                'day_range': day_data['high'].max() - day_data['low'].min() if not day_data.empty else None,
                'total_candles': len(day_data)
            },
            
            'validated_observations': self.structure_validated_observations(validation_results),
            'pattern_library': self.extract_pattern_library(your_analysis, validation_results),
            'time_based_analysis': self.extract_time_analysis(your_analysis, validation_results),
            'price_level_analysis': self.extract_price_levels(your_analysis, day_data),
            'market_structure_insights': self.extract_market_structure(your_analysis),
            
            'ai_training_features': {
                'pattern_recognition_style': self.analyze_pattern_style(your_analysis),
                'timing_methodology': self.analyze_timing_approach(your_analysis),
                'risk_management_approach': self.analyze_risk_approach(your_analysis),
                'market_context_integration': self.analyze_context_usage(your_analysis),
                'prediction_framework': self.extract_predictions(your_analysis)
            },
            
            'improvement_suggestions': self.generate_specific_improvements(validation_results, your_analysis)
        }
        
        return enhanced_obs
    
    def calculate_validation_score(self, validation_results: Dict) -> Dict:
        """Calculate validation score breakdown"""
        validations = validation_results.get('validations', [])
        
        validated = sum(1 for v in validations if v['validation'].get('status') == 'VALIDATED')
        partial = sum(1 for v in validations if v['validation'].get('status') == 'PARTIAL')
        needs_review = sum(1 for v in validations if v['validation'].get('status') == 'NEEDS_REVIEW')
        contradicted = sum(1 for v in validations if v['validation'].get('status') == 'CONTRADICTED')
        
        total = len(validations)
        
        return {
            'total_claims': total,
            'validated': validated,
            'partial': partial,
            'needs_review': needs_review,
            'contradicted': contradicted,
            'accuracy_percentage': (validated / total * 100) if total > 0 else 0,
            'confidence_score': ((validated + partial * 0.5) / total * 100) if total > 0 else 0
        }
    
    def extract_key_phrases(self, text: str) -> List[str]:
        """Extract key trading phrases from your analysis"""
        key_phrases = []
        
        # Pattern-related phrases
        pattern_indicators = [
            'inverted hammer', 'hanging man', 'spinning top', 'engulfing', 'marubozu',
            'parallel channel', 'head and shoulder', 'double top', 'double bottom',
            'support', 'resistance', 'breakout', 'breakdown', 'gap up', 'gap down'
        ]
        
        # Time-based phrases
        time_phrases = [
            'till', 'from', 'at', 'around', 'between', 'during', 'after', 'before'
        ]
        
        # Market direction phrases
        direction_phrases = [
            'bullish', 'bearish', 'sideways', 'upside', 'downside', 'continues', 'reversal'
        ]
        
        text_lower = text.lower()
        
        for phrase in pattern_indicators + time_phrases + direction_phrases:
            if phrase in text_lower:
                key_phrases.append(phrase)
        
        return list(set(key_phrases))
    
    def structure_validated_observations(self, validation_results: Dict) -> List[Dict]:
        """Structure validated observations for AI learning"""
        structured = []
        
        for validation in validation_results.get('validations', []):
            val_data = validation['validation']
            
            structured_val = {
                'claim': validation['claim'],
                'status': val_data.get('status'),
                'confidence': self.map_status_to_confidence(val_data.get('status')),
                'data_points': {}
            }
            
            # Add relevant data points based on validation type
            if 'move_points' in val_data:
                structured_val['data_points'] = {
                    'start_price': val_data.get('start_price'),
                    'end_price': val_data.get('end_price'),
                    'move_points': val_data.get('move_points'),
                    'move_percentage': val_data.get('move_percentage'),
                    'direction': val_data.get('actual_direction')
                }
            elif 'actual_gap' in val_data:
                structured_val['data_points'] = {
                    'claimed_gap': val_data.get('claimed_gap'),
                    'actual_gap': val_data.get('actual_gap'),
                    'gap_accuracy': val_data.get('gap_accuracy')
                }
            elif 'identified_pattern' in val_data:
                structured_val['data_points'] = {
                    'claimed_pattern': val_data.get('claimed_pattern'),
                    'identified_pattern': val_data.get('identified_pattern'),
                    'candle_data': val_data.get('candle_data')
                }
            
            structured.append(structured_val)
        
        return structured
    
    def map_status_to_confidence(self, status: str) -> float:
        """Map validation status to confidence score"""
        mapping = {
            'VALIDATED': 1.0,
            'PARTIAL': 0.6,
            'NEEDS_REVIEW': 0.3,
            'CONTRADICTED': 0.0
        }
        return mapping.get(status, 0.5)
    
    def extract_pattern_library(self, analysis: str, validation_results: Dict) -> Dict:
        """Extract pattern library from your analysis"""
        patterns = {
            'candlestick_patterns': [],
            'chart_patterns': [],
            'support_resistance': [],
            'trend_patterns': []
        }
        
        # Extract from your text
        text_lower = analysis.lower()
        
        # Candlestick patterns
        candlestick_keywords = ['hammer', 'hanging man', 'spinning top', 'engulfing', 'marubozu']
        for keyword in candlestick_keywords:
            if keyword in text_lower:
                patterns['candlestick_patterns'].append({
                    'pattern': keyword,
                    'context': self.get_context_around_keyword(analysis, keyword),
                    'validation_status': self.get_pattern_validation_status(keyword, validation_results)
                })
        
        # Chart patterns
        chart_keywords = ['channel', 'head and shoulder', 'double', 'triangle', 'wedge']
        for keyword in chart_keywords:
            if keyword in text_lower:
                patterns['chart_patterns'].append({
                    'pattern': keyword,
                    'context': self.get_context_around_keyword(analysis, keyword),
                    'validation_status': self.get_pattern_validation_status(keyword, validation_results)
                })
        
        return patterns
    
    def get_context_around_keyword(self, text: str, keyword: str, context_words: int = 10) -> str:
        """Get context around a keyword"""
        words = text.split()
        keyword_indices = [i for i, word in enumerate(words) if keyword.lower() in word.lower()]
        
        contexts = []
        for idx in keyword_indices:
            start = max(0, idx - context_words)
            end = min(len(words), idx + context_words + 1)
            context = ' '.join(words[start:end])
            contexts.append(context)
        
        return ' | '.join(contexts)
    
    def get_pattern_validation_status(self, pattern: str, validation_results: Dict) -> str:
        """Get validation status for a specific pattern"""
        for validation in validation_results.get('validations', []):
            if pattern.lower() in validation['claim'].lower():
                return validation['validation'].get('status', 'UNKNOWN')
        return 'NOT_VALIDATED'
    
    def extract_time_analysis(self, analysis: str, validation_results: Dict) -> Dict:
        """Extract time-based analysis patterns"""
        import re
        
        time_patterns = re.findall(r'\d{1,2}:\d{2}', analysis)
        time_ranges = re.findall(r'from.*?to.*?\d{1,2}:\d{2}', analysis)
        
        return {
            'specific_times_mentioned': time_patterns,
            'time_ranges_identified': time_ranges,
            'session_based_analysis': 'session' in analysis.lower() or 'opening' in analysis.lower(),
            'intraday_timing_precision': len(time_patterns) > 5,
            'time_based_predictions': self.extract_time_predictions(analysis)
        }
    
    def extract_time_predictions(self, analysis: str) -> List[str]:
        """Extract time-based predictions"""
        predictions = []
        sentences = analysis.split('.')
        
        for sentence in sentences:
            if any(word in sentence.lower() for word in ['till', 'until', 'from', 'after', 'before']):
                if any(word in sentence.lower() for word in ['will', 'expect', 'should', 'likely']):
                    predictions.append(sentence.strip())
        
        return predictions
    
    def extract_price_levels(self, analysis: str, day_data: pd.DataFrame) -> Dict:
        """Extract price level analysis"""
        import re
        
        # Extract numbers that could be price levels
        price_numbers = re.findall(r'\d{4,5}(?:\.\d{1,2})?', analysis)
        price_levels = [float(p) for p in price_numbers if 8000 <= float(p) <= 15000]  # Reasonable Nifty range
        
        if day_data.empty:
            return {'price_levels_mentioned': price_levels}
        
        day_high = day_data['high'].max()
        day_low = day_data['low'].min()
        
        # Categorize price levels
        support_levels = [p for p in price_levels if p <= day_low * 1.01]
        resistance_levels = [p for p in price_levels if p >= day_high * 0.99]
        
        return {
            'price_levels_mentioned': price_levels,
            'potential_support_levels': support_levels,
            'potential_resistance_levels': resistance_levels,
            'price_precision': len([p for p in price_numbers if '.' in p]) > 0,
            'relative_to_day_range': {
                'above_high': len([p for p in price_levels if p > day_high]),
                'below_low': len([p for p in price_levels if p < day_low]),
                'within_range': len([p for p in price_levels if day_low <= p <= day_high])
            }
        }
    
    def extract_market_structure(self, analysis: str) -> Dict:
        """Extract market structure insights"""
        structure_insights = {
            'trend_analysis': [],
            'phase_identification': [],
            'pattern_sequence': [],
            'cause_effect_relationships': []
        }
        
        # Trend analysis
        if 'bullish' in analysis.lower():
            structure_insights['trend_analysis'].append('bullish_bias_identified')
        if 'bearish' in analysis.lower():
            structure_insights['trend_analysis'].append('bearish_bias_identified')
        if 'sideways' in analysis.lower():
            structure_insights['trend_analysis'].append('sideways_movement_identified')
        
        # Phase identification
        if 'phase' in analysis.lower() or 'session' in analysis.lower():
            structure_insights['phase_identification'].append('session_based_thinking')
        
        # Pattern sequence
        if 'then' in analysis.lower() or 'after' in analysis.lower():
            structure_insights['pattern_sequence'].append('sequential_pattern_recognition')
        
        return structure_insights
    
    def analyze_pattern_style(self, analysis: str) -> Dict:
        """Analyze your pattern recognition style"""
        return {
            'candlestick_focused': analysis.lower().count('hammer') + analysis.lower().count('hanging') > 2,
            'chart_pattern_focused': 'channel' in analysis.lower() or 'triangle' in analysis.lower(),
            'multi_timeframe': 'day' in analysis.lower() and 'candle' in analysis.lower(),
            'detail_oriented': len(analysis.split()) > 100,
            'technical_terminology': len([word for word in analysis.split() if word.lower() in 
                                        ['engulfing', 'marubozu', 'doji', 'harami']]) > 0
        }
    
    def analyze_timing_approach(self, analysis: str) -> Dict:
        """Analyze your timing methodology"""
        import re
        
        time_mentions = len(re.findall(r'\d{1,2}:\d{2}', analysis))
        
        return {
            'precise_timing': time_mentions > 5,
            'session_based': 'opening' in analysis.lower() or 'closing' in analysis.lower(),
            'intraday_focused': time_mentions > 0,
            'sequence_aware': 'then' in analysis.lower() or 'after' in analysis.lower(),
            'time_correlation': 'from' in analysis.lower() and 'to' in analysis.lower()
        }
    
    def analyze_risk_approach(self, analysis: str) -> Dict:
        """Analyze your risk management approach"""
        return {
            'stop_loss_mentioned': 'stop' in analysis.lower(),
            'target_levels_specified': 'target' in analysis.lower(),
            'risk_reward_considered': 'risk' in analysis.lower() and 'reward' in analysis.lower(),
            'failure_scenarios': 'fail' in analysis.lower() or 'break' in analysis.lower(),
            'confirmation_required': 'confirm' in analysis.lower() or 'validation' in analysis.lower()
        }
    
    def analyze_context_usage(self, analysis: str) -> Dict:
        """Analyze how you use market context"""
        return {
            'previous_day_reference': 'previous' in analysis.lower() or 'yesterday' in analysis.lower(),
            'gap_analysis': 'gap' in analysis.lower(),
            'volume_consideration': 'volume' in analysis.lower(),
            'multi_day_perspective': 'day' in analysis.lower() and len(analysis.split()) > 50,
            'pattern_continuation': 'continue' in analysis.lower() or 'follow' in analysis.lower()
        }
    
    def extract_predictions(self, analysis: str) -> List[Dict]:
        """Extract your prediction framework"""
        predictions = []
        sentences = analysis.split('.')
        
        prediction_keywords = ['expect', 'will', 'should', 'likely', 'target', 'break', 'continue']
        
        for sentence in sentences:
            for keyword in prediction_keywords:
                if keyword in sentence.lower():
                    predictions.append({
                        'prediction_text': sentence.strip(),
                        'prediction_type': keyword,
                        'confidence_indicators': self.extract_confidence_indicators(sentence)
                    })
                    break
        
        return predictions
    
    def extract_confidence_indicators(self, sentence: str) -> List[str]:
        """Extract confidence indicators from sentence"""
        confidence_words = ['strong', 'weak', 'clear', 'obvious', 'likely', 'probable', 'certain']
        return [word for word in confidence_words if word in sentence.lower()]
    
    def generate_specific_improvements(self, validation_results: Dict, analysis: str) -> Dict:
        """Generate specific improvements based on validation results"""
        improvements = {
            'immediate_fixes': [],
            'pattern_refinements': [],
            'measurement_enhancements': [],
            'context_additions': [],
            'prediction_improvements': []
        }
        
        validation_score = self.calculate_validation_score(validation_results)
        
        # Based on accuracy score
        if validation_score['accuracy_percentage'] < 60:
            improvements['immediate_fixes'].extend([
                "Add exact price levels for each claim",
                "Include percentage moves for quantification",
                "Specify exact time ranges (HH:MM to HH:MM)",
                "Add volume confirmation where possible"
            ])
        
        # Pattern-specific improvements
        needs_review_patterns = [v for v in validation_results.get('validations', []) 
                               if v['validation'].get('status') == 'NEEDS_REVIEW']
        
        if needs_review_patterns:
            improvements['pattern_refinements'].extend([
                "Define exact shadow-to-body ratios for candlestick patterns",
                "Add pattern confirmation criteria",
                "Include pattern failure conditions",
                "Specify minimum pattern size requirements"
            ])
        
        # Measurement enhancements
        improvements['measurement_enhancements'].extend([
            f"Current analysis length: {len(analysis.split())} words - consider adding more detail",
            "Add risk-reward ratios for each setup",
            "Include confidence levels (1-10) for each observation",
            "Add stop-loss levels for risk management"
        ])
        
        return improvements

def main():
    """Main function to create enhanced AI training data"""
    
    print("🤖 CREATING ENHANCED AI TRAINING DATA")
    print("=" * 50)
    
    converter = EnhancedAITrainingConverter()
    
    # Load validation results
    try:
        with open('two_day_validation_results.json', 'r') as f:
            validation_data = json.load(f)
    except FileNotFoundError:
        print("❌ Validation results not found. Run validation first.")
        return
    
    # Your observations
    observations = {
        '2018-01-01': """So market opens around 10 points gapup from yesterdays close 10467.40 and remained sideways to bearish till the close of 1:20. and from 1:25 it give one bullish move till 2:20 close and from 2:25 candle it started bearish move and with one green candle which is somewhat looking inverted hammer at 2:40 the bearish move intesifies with the inverted hanging man at 2:45 and the 3:05 candle forms the hanging man at the bottom
Throughout the day inverted hammer was taking market upside and at 11:35 that fails and the bearish trend continues
In other terms we can say that market was in the parallel channel from 9:25 to 2:45 and the 2:50 candle breaks the parallel channel with big IFC""",
        
        '2018-01-02': """So market formed the w previous day from the 3:00 to 3:25 then opened gapup around 45 points with inverted hanging man with long upper shadow and then market continues the bearish stance till 10:05 candle and with that candle and its next candle market forms the bullish engulfing and goes upside till 10:45. the 10:40 candle is similar to the 9:15 candle just somewhat small which shows the bearish sign the 9:40 candle to 10:25 candle market forms the small inverted head n shoulder then the retesting/ liquidity absrption took place till 11:20 candle and then went upside. The closing point of the 10:05 acted as the support and market took one tap support from that level at 2:15 candle. In terms of the supply, which came form 9:15 candle till 10:05 candle is the double supply of the 1:50 to the 2:15 candle and then at 2:35 market forms the proper Green Hammer and went upside till 3:00 same demand which formed from 10:10 candle to 10:45 candle and forms red spinning top candle at 3:05 showing indecisiveness, but in spinning top cases if the next 2 to 3 candles breaks the high or low of the spinning top with body close market goes in that direction and at the end market forms inverted hammer at 3:25. the wick of the candle formed on 1 jan 2018 3:20 acted as an the support Resistance interchange (SR interchange). It worked as the support on 9:50 candle and then breaks that on 10:00 and then again act as resistance on 10:15, breaks that on 10:20 again act as an support on 11:25 as well as 1:00 and then breaks that support on the 1:55 and then 2:05 candle breaks the resistance but leaves only the shadow didn't closes the body above SR interchange and the breaks the resistance on 2:45 and the next candle to it act as and support to the SR"""
    }
    
    # Create enhanced training data for both days
    enhanced_training_data = {
        'metadata': {
            'created_at': datetime.now().isoformat(),
            'total_observations': len(observations),
            'analyst_profile': 'Detailed intraday pattern analyst with focus on candlestick patterns and support/resistance'
        },
        'observations': {}
    }
    
    for date, analysis in observations.items():
        validation_key = date.replace('-', '_')
        validation_results = validation_data.get(f'jan_{validation_key.split("_")[2]}_{validation_key.split("_")[0]}', {})
        
        enhanced_obs = converter.create_enhanced_observation_format(date, analysis, validation_results)
        enhanced_training_data['observations'][date] = enhanced_obs
        
        print(f"\n📅 {date} - Enhanced Training Data Created")
        print(f"   Validation Score: {enhanced_obs['observation_metadata']['validation_score']['accuracy_percentage']:.1f}%")
        print(f"   Key Phrases: {len(enhanced_obs['raw_analysis']['key_phrases'])}")
        print(f"   Patterns Identified: {len(enhanced_obs['pattern_library']['candlestick_patterns']) + len(enhanced_obs['pattern_library']['chart_patterns'])}")
        print(f"   Improvement Suggestions: {len(enhanced_obs['improvement_suggestions']['immediate_fixes'])}")
    
    # Save enhanced training data
    with open('enhanced_ai_training_data.json', 'w') as f:
        json.dump(enhanced_training_data, f, indent=2, default=str)
    
    print(f"\n✅ Enhanced AI training data saved to 'enhanced_ai_training_data.json'")
    
    # Show key insights
    print(f"\n🎯 KEY INSIGHTS FOR AI TRAINING:")
    for date, obs in enhanced_training_data['observations'].items():
        print(f"\n{date}:")
        ai_features = obs['ai_training_features']
        print(f"  • Pattern Style: {'Candlestick-focused' if ai_features['pattern_recognition_style']['candlestick_focused'] else 'Chart-pattern focused'}")
        print(f"  • Timing Approach: {'Precise' if ai_features['timing_methodology']['precise_timing'] else 'General'}")
        print(f"  • Detail Level: {'High' if ai_features['pattern_recognition_style']['detail_oriented'] else 'Medium'}")
        print(f"  • Predictions Made: {len(ai_features['prediction_framework'])}")
    
    return enhanced_training_data

if __name__ == "__main__":
    enhanced_data = main()
