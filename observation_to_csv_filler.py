"""
Observation to CSV Filler
Converts your natural language observations into structured CSV data
with precise pinpointing for AI training
"""

import pandas as pd
import re
from datetime import datetime, time
from typing import Dict, List, Tuple
import json

class ObservationToCSVFiller:
    def __init__(self, csv_path: str = "Nifty.csv"):
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        
        # Your column mapping
        self.columns = {
            'obs_id': 'Observation ID',
            'market_condition': 'Market Condition',
            'pattern_start': 'Pattern Start Time',
            'pattern_end': 'Pattern End Time',
            'pattern_name': 'Pattern Name',
            'nature': 'Nature of Pattern',
            'reasoning': 'Your Reasoning',
            'expected_outcome': 'Expected Outcome'
        }
        
    def parse_your_observation(self, observation_text: str, observation_date: str) -> Dict:
        """Parse your natural language observation into structured components"""
        
        # Your January 1, 2018 observation
        jan_1_analysis = {
            'observation_id': 'JAN_01_2018_001',
            'observation_date': observation_date,
            'full_text': observation_text,
            
            # Extract key components from your text
            'market_phases': [
                {
                    'phase': 'sideways_bearish',
                    'start_time': '09:20',
                    'end_time': '13:20',
                    'condition': 'Sideways to Bearish',
                    'reasoning': 'Market remained sideways to bearish till 1:20 close'
                },
                {
                    'phase': 'bullish_attempt',
                    'start_time': '13:25',
                    'end_time': '14:20',
                    'condition': 'Bullish',
                    'reasoning': 'One bullish move from 1:25 to 2:20 close'
                },
                {
                    'phase': 'bearish_breakdown',
                    'start_time': '14:25',
                    'end_time': '15:25',
                    'condition': 'Strong Bearish',
                    'reasoning': 'Bearish move intensifies after 2:25 candle'
                }
            ],
            
            # Specific patterns identified
            'patterns': [
                {
                    'name': 'Parallel Channel',
                    'start_time': '09:25',
                    'end_time': '14:45',
                    'nature': 'Continuation',
                    'reasoning': 'Market was in parallel channel from 9:25 to 2:45',
                    'outcome': 'Channel break expected'
                },
                {
                    'name': 'Channel Breakdown',
                    'start_time': '14:50',
                    'end_time': '14:50',
                    'nature': 'Reversal/Breakdown',
                    'reasoning': '2:50 candle breaks parallel channel with big IFC',
                    'outcome': 'Further bearish move expected'
                },
                {
                    'name': 'Inverted Hammer',
                    'start_time': '14:40',
                    'end_time': '14:40',
                    'nature': 'Reversal Signal',
                    'reasoning': 'Green candle looking like inverted hammer at 2:40',
                    'outcome': 'Failed reversal, bearish continues'
                },
                {
                    'name': 'Hanging Man',
                    'start_time': '14:45',
                    'end_time': '14:45',
                    'nature': 'Reversal Signal',
                    'reasoning': 'Inverted hanging man at 2:45',
                    'outcome': 'Bearish intensification'
                },
                {
                    'name': 'Hanging Man Bottom',
                    'start_time': '15:05',
                    'end_time': '15:05',
                    'nature': 'Bottom Signal',
                    'reasoning': '3:05 candle forms hanging man at the bottom',
                    'outcome': 'Potential bottom formation'
                }
            ]
        }
        
        return jan_1_analysis
    
    def time_to_datetime(self, date_str: str, time_str: str) -> datetime:
        """Convert date and time strings to datetime"""
        try:
            # Handle different time formats
            if ':' in time_str:
                hour, minute = time_str.split(':')
            else:
                hour = time_str[:2] if len(time_str) >= 2 else time_str
                minute = time_str[2:] if len(time_str) > 2 else '00'
            
            return pd.to_datetime(f"{date_str} {hour}:{minute}")
        except:
            return pd.to_datetime(f"{date_str} 09:20")  # Default fallback
    
    def find_matching_rows(self, target_datetime: datetime, tolerance_minutes: int = 5) -> List[int]:
        """Find CSV rows that match the target datetime within tolerance"""
        time_diff = abs(self.df['datetime'] - target_datetime)
        tolerance = pd.Timedelta(minutes=tolerance_minutes)
        matching_indices = self.df[time_diff <= tolerance].index.tolist()
        return matching_indices
    
    def fill_csv_with_observations(self, parsed_observation: Dict) -> pd.DataFrame:
        """Fill CSV columns with your structured observations"""
        
        # Create a copy of the dataframe to modify
        filled_df = self.df.copy()
        
        observation_date = parsed_observation['observation_date']
        obs_id = parsed_observation['observation_id']
        
        # Fill market phases
        for phase in parsed_observation['market_phases']:
            start_dt = self.time_to_datetime(observation_date, phase['start_time'])
            end_dt = self.time_to_datetime(observation_date, phase['end_time'])
            
            # Find all rows in this time range
            mask = (filled_df['datetime'] >= start_dt) & (filled_df['datetime'] <= end_dt)
            matching_rows = filled_df[mask]
            
            if not matching_rows.empty:
                # Fill the matching rows
                filled_df.loc[mask, self.columns['obs_id']] = f"{obs_id}_PHASE_{phase['phase'].upper()}"
                filled_df.loc[mask, self.columns['market_condition']] = phase['condition']
                filled_df.loc[mask, self.columns['reasoning']] = phase['reasoning']
                
                print(f"✅ Filled {len(matching_rows)} rows for {phase['phase']} phase ({phase['start_time']}-{phase['end_time']})")
        
        # Fill specific patterns
        for i, pattern in enumerate(parsed_observation['patterns']):
            start_dt = self.time_to_datetime(observation_date, pattern['start_time'])
            end_dt = self.time_to_datetime(observation_date, pattern['end_time'])
            
            # For single-candle patterns, find the closest match
            if pattern['start_time'] == pattern['end_time']:
                matching_indices = self.find_matching_rows(start_dt)
            else:
                # For multi-candle patterns, find range
                mask = (filled_df['datetime'] >= start_dt) & (filled_df['datetime'] <= end_dt)
                matching_indices = filled_df[mask].index.tolist()
            
            if matching_indices:
                pattern_id = f"{obs_id}_PATTERN_{i+1}_{pattern['name'].replace(' ', '_').upper()}"
                
                for idx in matching_indices:
                    # Fill pattern-specific data
                    filled_df.loc[idx, self.columns['obs_id']] = pattern_id
                    filled_df.loc[idx, self.columns['pattern_start']] = pattern['start_time']
                    filled_df.loc[idx, self.columns['pattern_end']] = pattern['end_time']
                    filled_df.loc[idx, self.columns['pattern_name']] = pattern['name']
                    filled_df.loc[idx, self.columns['nature']] = pattern['nature']
                    filled_df.loc[idx, self.columns['reasoning']] = pattern['reasoning']
                    filled_df.loc[idx, self.columns['expected_outcome']] = pattern['outcome']
                
                print(f"✅ Filled pattern '{pattern['name']}' at {pattern['start_time']} ({len(matching_indices)} rows)")
        
        return filled_df
    
    def create_observation_summary(self, parsed_observation: Dict) -> Dict:
        """Create a summary of what will be filled"""
        summary = {
            'observation_id': parsed_observation['observation_id'],
            'date': parsed_observation['observation_date'],
            'total_phases': len(parsed_observation['market_phases']),
            'total_patterns': len(parsed_observation['patterns']),
            'time_coverage': {},
            'patterns_identified': []
        }
        
        # Calculate time coverage
        all_times = []
        for phase in parsed_observation['market_phases']:
            all_times.extend([phase['start_time'], phase['end_time']])
        for pattern in parsed_observation['patterns']:
            all_times.extend([pattern['start_time'], pattern['end_time']])
        
        if all_times:
            summary['time_coverage'] = {
                'earliest': min(all_times),
                'latest': max(all_times),
                'total_timepoints': len(set(all_times))
            }
        
        # List all patterns
        for pattern in parsed_observation['patterns']:
            summary['patterns_identified'].append({
                'name': pattern['name'],
                'time': f"{pattern['start_time']}-{pattern['end_time']}",
                'nature': pattern['nature']
            })
        
        return summary
    
    def validate_filled_data(self, filled_df: pd.DataFrame, observation_date: str) -> Dict:
        """Validate the filled data against actual OHLC"""
        
        # Get data for the observation date
        date_mask = filled_df['datetime'].dt.date == pd.to_datetime(observation_date).date()
        day_data = filled_df[date_mask]
        
        # Count filled rows
        filled_rows = day_data[day_data[self.columns['obs_id']].notna()]
        
        validation = {
            'total_candles_for_date': len(day_data),
            'filled_candles': len(filled_rows),
            'fill_percentage': (len(filled_rows) / len(day_data)) * 100 if len(day_data) > 0 else 0,
            'unique_observation_ids': filled_rows[self.columns['obs_id']].nunique(),
            'patterns_filled': filled_rows[self.columns['pattern_name']].nunique(),
            'market_conditions_identified': filled_rows[self.columns['market_condition']].nunique()
        }
        
        return validation
    
    def save_filled_csv(self, filled_df: pd.DataFrame, output_path: str = None):
        """Save the filled CSV"""
        if output_path is None:
            output_path = self.csv_path.replace('.csv', '_filled.csv')
        
        filled_df.to_csv(output_path, index=False)
        print(f"💾 Filled CSV saved to: {output_path}")
        return output_path

def main():
    """Main function to process your observation"""
    
    print("🔄 CONVERTING YOUR OBSERVATION TO CSV TRAINING DATA")
    print("=" * 60)
    
    # Your January 1, 2018 observation
    your_observation = """
    So market opens 10 points gapup from yesterdays close and remaind sideways to bearish till the close of 1:20. 
    and from 1:25 it give one bullish move till 2:20 close and from 2:25 candle it started bearish move and with one 
    green candle which is somewhat looking inverted hammer at 2:40 the bearish move intesifies with the inverted 
    hanging man at 2:45 and the 3:05 candle forms the hanging man at the bottom
    Throughout the day inverted hammer was taking market upside and at 11:35 that fails and the bearish trend continues
    In other terms we can say that market was in the parallel channel from 9:25 to 2:45 and the 2:50 candle breaks 
    the parallel channel with big IFC
    """
    
    # Initialize the filler
    filler = ObservationToCSVFiller()
    
    # Parse your observation
    print("📝 Parsing your natural language observation...")
    parsed_obs = filler.parse_your_observation(your_observation, '2018-01-01')
    
    # Create summary
    summary = filler.create_observation_summary(parsed_obs)
    print(f"\n📊 OBSERVATION SUMMARY:")
    print(f"• Observation ID: {summary['observation_id']}")
    print(f"• Date: {summary['date']}")
    print(f"• Market Phases: {summary['total_phases']}")
    print(f"• Patterns Identified: {summary['total_patterns']}")
    print(f"• Time Coverage: {summary['time_coverage']['earliest']} to {summary['time_coverage']['latest']}")
    
    print(f"\n🎯 PATTERNS TO BE FILLED:")
    for pattern in summary['patterns_identified']:
        print(f"• {pattern['name']} ({pattern['time']}) - {pattern['nature']}")
    
    # Fill the CSV
    print(f"\n🔄 Filling CSV with structured data...")
    filled_df = filler.fill_csv_with_observations(parsed_obs)
    
    # Validate
    validation = filler.validate_filled_data(filled_df, '2018-01-01')
    print(f"\n✅ VALIDATION RESULTS:")
    print(f"• Total candles for Jan 1, 2018: {validation['total_candles_for_date']}")
    print(f"• Filled candles: {validation['filled_candles']}")
    print(f"• Fill percentage: {validation['fill_percentage']:.1f}%")
    print(f"• Unique observation IDs: {validation['unique_observation_ids']}")
    print(f"• Patterns filled: {validation['patterns_filled']}")
    print(f"• Market conditions: {validation['market_conditions_identified']}")
    
    # Save filled CSV
    output_path = filler.save_filled_csv(filled_df)
    
    print(f"\n🎉 SUCCESS!")
    print(f"✅ Your natural language observation converted to structured CSV data")
    print(f"📁 Saved to: {output_path}")
    print(f"🤖 Ready for AI training with precise pinpointing!")
    
    # Show sample of filled data
    print(f"\n📋 SAMPLE OF FILLED DATA:")
    jan_1_data = filled_df[filled_df['datetime'].dt.date == pd.to_datetime('2018-01-01').date()]
    filled_sample = jan_1_data[jan_1_data['Observation ID'].notna()].head(10)
    
    if not filled_sample.empty:
        display_cols = ['datetime', 'open', 'high', 'low', 'close', 'Observation ID', 'Pattern Name', 'Market Condition']
        print(filled_sample[display_cols].to_string(index=False))
    
    return filled_df, validation

if __name__ == "__main__":
    filled_df, validation = main()
