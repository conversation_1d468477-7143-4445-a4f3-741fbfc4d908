"""
Demo: How to use the Market Self-Analysis AI System
This demonstrates the complete workflow for your market analysis
"""

from market_analysis_framework import MarketAnalysisAI
from observation_recorder import ObservationRecorder
import json

def demo_analysis_workflow():
    """Demonstrate the complete analysis workflow"""
    
    print("=== Market Self-Analysis AI Demo ===\n")
    
    # Step 1: Initialize the system
    print("1. Initializing Market Analysis AI...")
    analyzer = MarketAnalysisAI()
    recorder = ObservationRecorder()
    
    # Step 2: Analyze current market structure
    print("\n2. Analyzing current Nifty market structure...")
    nifty_analysis = analyzer.analyze_market_structure('Nifty')
    
    print(f"Current Nifty Price: {nifty_analysis['current_price']:.2f}")
    print(f"Trend: {nifty_analysis['trend']}")
    print(f"RSI: {nifty_analysis['technical_indicators']['RSI']:.2f}")
    print(f"Recent Range: {nifty_analysis['recent_range']['range_percent']:.2f}%")
    
    # Step 3: Create observation template for specific analysis
    print("\n3. Creating observation template for specific time...")
    template = analyzer.create_observation_template('Nifty', '2025-08-08 15:25:00')
    
    print("Price data at 2025-08-08 15:25:00:")
    print(f"Open: {template['price_data']['open']}")
    print(f"High: {template['price_data']['high']}")
    print(f"Low: {template['price_data']['low']}")
    print(f"Close: {template['price_data']['close']}")
    
    # Step 4: Example of adding a human observation
    print("\n4. Example: Adding a human observation...")
    
    sample_observation = recorder.create_new_observation(
        index="Nifty",
        analysis_datetime="2025-08-08 15:25:00",
        observation_text="Strong support at 24345 level. Price bounced with good volume. RSI showing oversold bounce pattern.",
        pattern_type="Support Bounce",
        confidence="High",
        prediction="Expect move towards 24400-24450 resistance zone",
        key_levels={
            'support': [24345, 24300],
            'resistance': [24400, 24450]
        }
    )
    
    # Add additional context to the observation
    sample_observation['market_context']['overall_trend'] = "Sideways with bullish bias"
    sample_observation['market_context']['volume_analysis'] = "Higher volume on bounce from support"
    sample_observation['technical_analysis']['key_price_points'] = [24345, 24400]
    
    print("Sample observation created:")
    print(f"Pattern: {sample_observation['observation']['pattern_type']}")
    print(f"Confidence: {sample_observation['observation']['confidence_level']}")
    print(f"Prediction: {sample_observation['observation']['prediction']}")
    
    # Step 5: Show how to track outcomes
    print("\n5. Example: Tracking observation outcomes...")
    
    # Simulate updating the outcome after some time
    outcome_data = {
        'actual_outcome': 'Target of 24400 achieved in next session',
        'prediction_accuracy': '9/10',
        'lessons_learned': 'Volume confirmation at support was key signal',
        'follow_up_date': '2025-08-09'
    }
    
    sample_observation['outcome_tracking'].update(outcome_data)
    
    print("Outcome tracking updated:")
    print(f"Accuracy: {outcome_data['prediction_accuracy']}")
    print(f"Lesson: {outcome_data['lessons_learned']}")
    
    return analyzer, recorder, sample_observation

def show_analysis_capabilities():
    """Show what analysis capabilities you have with current data"""
    
    print("\n=== Analysis Capabilities with Your Current Data ===\n")
    
    analyzer = MarketAnalysisAI()
    
    # Analyze all indices
    indices = ['Nifty', 'Banknifty', 'Finnifty', 'Midcap', 'Sensex']
    
    for index in indices:
        print(f"\n--- {index} Analysis ---")
        analysis = analyzer.analyze_market_structure(index)
        
        print(f"Current Price: {analysis['current_price']:.2f}")
        print(f"Trend: {analysis['trend']}")
        print(f"RSI: {analysis['technical_indicators']['RSI']:.2f}")
        
        # Show key levels
        swing_highs = analysis['key_levels']['swing_highs'][-3:]  # Last 3
        swing_lows = analysis['key_levels']['swing_lows'][-3:]    # Last 3
        
        if swing_highs:
            print("Recent Swing Highs:")
            for high in swing_highs:
                print(f"  {high['datetime'].strftime('%Y-%m-%d')}: {high['price']:.2f}")
        
        if swing_lows:
            print("Recent Swing Lows:")
            for low in swing_lows:
                print(f"  {low['datetime'].strftime('%Y-%m-%d')}: {low['price']:.2f}")

def practical_usage_guide():
    """Show practical steps for using the system"""
    
    print("\n=== Practical Usage Guide ===\n")
    
    print("STEP-BY-STEP WORKFLOW:")
    print("1. Open your charting software (TradingView, etc.)")
    print("2. Analyze the chart visually")
    print("3. Identify key patterns, levels, and setups")
    print("4. Use the observation recorder to document your analysis")
    print("5. Include specific price levels and time stamps")
    print("6. Make predictions with confidence levels")
    print("7. Track outcomes to improve accuracy")
    print("8. Build a database of successful patterns")
    
    print("\nEXAMPLE OBSERVATION PROCESS:")
    print("- Chart Analysis: 'Nifty showing double bottom at 24300'")
    print("- Key Levels: Support at 24300, 24250; Resistance at 24450, 24500")
    print("- Technical: RSI oversold, MACD bullish divergence")
    print("- Prediction: 'Expect bounce to 24450 with 80% confidence'")
    print("- Risk: 'Stop loss below 24250'")
    
    print("\nDATA YOU CAN ANALYZE RIGHT NOW:")
    print("✅ Price patterns and trends")
    print("✅ Support and resistance levels")
    print("✅ Technical indicators (RSI, MACD, Moving Averages)")
    print("✅ Market structure analysis")
    print("✅ Historical pattern recognition")
    print("✅ Swing high/low identification")
    
    print("\nDATA TO ADD LATER (Priority Order):")
    print("1. Volume data (confirms price movements)")
    print("2. VIX data (market sentiment)")
    print("3. FII/DII data (institutional flows)")
    print("4. Global market data (correlations)")
    print("5. News/events data (fundamental context)")

if __name__ == "__main__":
    # Run the demo
    analyzer, recorder, sample_obs = demo_analysis_workflow()
    
    # Show current capabilities
    show_analysis_capabilities()
    
    # Show practical usage
    practical_usage_guide()
    
    print("\n=== Ready to Start! ===")
    print("Run 'python observation_recorder.py' to start recording your analysis!")
    print("Your OHLC data is already loaded and ready for analysis.")
