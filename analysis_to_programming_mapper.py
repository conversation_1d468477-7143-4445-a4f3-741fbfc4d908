"""
Analysis to Programming Mapper
Takes your analysis as ground truth and maps it to CSV data in programming terms
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Tuple
import numpy as np

class AnalysisToProgrammingMapper:
    def __init__(self, csv_path: str = "Nifty.csv"):
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path, low_memory=False)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        
    def get_exact_candle_data(self, date: str, time: str) -> Dict:
        """Get exact candle data for specific date and time"""
        target_datetime = pd.to_datetime(f"{date} {time}")
        
        # Find closest candle
        time_diff = abs(self.df['datetime'] - target_datetime)
        closest_idx = time_diff.idxmin()
        candle = self.df.iloc[closest_idx]
        
        return {
            'datetime': str(candle['datetime']),
            'open': candle['open'],
            'high': candle['high'],
            'low': candle['low'],
            'close': candle['close'],
            'csv_index': closest_idx,
            'time_accuracy': str(time_diff.iloc[closest_idx])
        }
    
    def get_range_data(self, date: str, start_time: str, end_time: str) -> Dict:
        """Get data for a time range"""
        start_dt = pd.to_datetime(f"{date} {start_time}")
        end_dt = pd.to_datetime(f"{date} {end_time}")
        
        mask = (self.df['datetime'] >= start_dt) & (self.df['datetime'] <= end_dt)
        range_data = self.df[mask].copy()
        
        if range_data.empty:
            return {'error': 'No data found for range'}
        
        return {
            'start_candle': {
                'datetime': str(range_data.iloc[0]['datetime']),
                'open': range_data.iloc[0]['open'],
                'close': range_data.iloc[0]['close']
            },
            'end_candle': {
                'datetime': str(range_data.iloc[-1]['datetime']),
                'open': range_data.iloc[-1]['open'],
                'close': range_data.iloc[-1]['close']
            },
            'range_stats': {
                'candle_count': len(range_data),
                'high': range_data['high'].max(),
                'low': range_data['low'].min(),
                'start_price': range_data.iloc[0]['close'],
                'end_price': range_data.iloc[-1]['close'],
                'net_move': range_data.iloc[-1]['close'] - range_data.iloc[0]['close'],
                'move_percentage': ((range_data.iloc[-1]['close'] - range_data.iloc[0]['close']) / range_data.iloc[0]['close']) * 100
            },
            'csv_indices': range_data.index.tolist()
        }
    
    def map_jan_1_2018_analysis(self) -> Dict:
        """Map January 1, 2018 analysis to programming terms"""
        
        your_analysis = """So market opens around 10 points gapup from yesterdays close 10467.40 and remained sideways to bearish till the close of 1:20. and from 1:25 it give one bullish move till 2:20 close and from 2:25 candle it started bearish move and with one green candle which is somewhat looking inverted hammer at 2:40 the bearish move intesifies with the inverted hanging man at 2:45 and the 3:05 candle forms the hanging man at the bottom
Throughout the day inverted hammer was taking market upside and at 11:35 that fails and the bearish trend continues
In other terms we can say that market was in the parallel channel from 9:25 to 2:45 and the 2:50 candle breaks the parallel channel with big IFC"""
        
        date = "2018-01-01"
        
        # Map your observations to programming terms
        programming_analysis = {
            'observation_id': 'JAN_01_2018_PROGRAMMING',
            'date': date,
            'your_original_analysis': your_analysis,
            
            'market_structure': {
                'gap_analysis': {
                    'your_claim': 'Market opens around 10 points gap up from yesterday close 10467.40',
                    'previous_close': 10467.40,
                    'actual_open_data': self.get_exact_candle_data(date, '09:15'),
                    'programming_terms': {
                        'gap_points': 'actual_open - previous_close',
                        'gap_percentage': '(gap_points / previous_close) * 100',
                        'gap_type': 'gap_up if gap_points > 0 else gap_down'
                    }
                },
                
                'session_phases': [
                    {
                        'phase_name': 'sideways_to_bearish',
                        'your_claim': 'remained sideways to bearish till the close of 1:20',
                        'time_range': {'start': '09:15', 'end': '13:20'},
                        'range_data': self.get_range_data(date, '09:15', '13:20'),
                        'programming_terms': {
                            'trend_direction': 'bearish if end_price < start_price else bullish',
                            'trend_strength': 'abs(end_price - start_price) / start_price * 100',
                            'phase_classification': 'sideways if abs(move_percentage) < 0.5 else trending'
                        }
                    },
                    {
                        'phase_name': 'bullish_move',
                        'your_claim': 'from 1:25 it give one bullish move till 2:20 close',
                        'time_range': {'start': '13:25', 'end': '14:20'},
                        'range_data': self.get_range_data(date, '13:25', '14:20'),
                        'programming_terms': {
                            'move_validation': 'end_price > start_price',
                            'move_strength': '(end_price - start_price) / start_price * 100',
                            'candle_count': 'number_of_candles_in_move'
                        }
                    },
                    {
                        'phase_name': 'bearish_breakdown',
                        'your_claim': 'from 2:25 candle it started bearish move',
                        'time_range': {'start': '14:25', 'end': '15:25'},
                        'range_data': self.get_range_data(date, '14:25', '15:25'),
                        'programming_terms': {
                            'breakdown_confirmation': 'end_price < start_price',
                            'breakdown_magnitude': 'abs(end_price - start_price)',
                            'breakdown_percentage': 'abs((end_price - start_price) / start_price) * 100'
                        }
                    }
                ]
            },
            
            'candlestick_patterns': [
                {
                    'pattern_name': 'inverted_hammer',
                    'your_claim': 'green candle which is somewhat looking inverted hammer at 2:40',
                    'time': '14:40',
                    'candle_data': self.get_exact_candle_data(date, '14:40'),
                    'programming_terms': {
                        'body_size': 'abs(close - open)',
                        'upper_shadow': 'high - max(open, close)',
                        'lower_shadow': 'min(open, close) - low',
                        'total_range': 'high - low',
                        'pattern_validation': 'upper_shadow > body_size * 2 and lower_shadow < body_size',
                        'color': 'green if close > open else red'
                    }
                },
                {
                    'pattern_name': 'hanging_man',
                    'your_claim': 'inverted hanging man at 2:45',
                    'time': '14:45',
                    'candle_data': self.get_exact_candle_data(date, '14:45'),
                    'programming_terms': {
                        'body_size': 'abs(close - open)',
                        'upper_shadow': 'high - max(open, close)',
                        'lower_shadow': 'min(open, close) - low',
                        'pattern_validation': 'lower_shadow > body_size * 2 and upper_shadow < body_size',
                        'bearish_signal': 'appears_after_uptrend and close < open'
                    }
                },
                {
                    'pattern_name': 'hanging_man_bottom',
                    'your_claim': '3:05 candle forms the hanging man at the bottom',
                    'time': '15:05',
                    'candle_data': self.get_exact_candle_data(date, '15:05'),
                    'programming_terms': {
                        'bottom_confirmation': 'candle_low == day_low or candle_low <= day_low * 1.001',
                        'reversal_potential': 'lower_shadow > body_size * 2',
                        'context': 'appears_at_support_level'
                    }
                }
            ],
            
            'chart_patterns': [
                {
                    'pattern_name': 'parallel_channel',
                    'your_claim': 'market was in the parallel channel from 9:25 to 2:45',
                    'time_range': {'start': '09:25', 'end': '14:45'},
                    'range_data': self.get_range_data(date, '09:25', '14:45'),
                    'programming_terms': {
                        'channel_top': 'resistance_level = max(highs_in_range)',
                        'channel_bottom': 'support_level = min(lows_in_range)',
                        'channel_width': 'channel_top - channel_bottom',
                        'channel_validation': 'price_oscillates_between_support_and_resistance',
                        'touch_points': 'count_of_support_resistance_tests'
                    }
                },
                {
                    'pattern_name': 'channel_breakdown',
                    'your_claim': '2:50 candle breaks the parallel channel with big IFC',
                    'time': '14:50',
                    'candle_data': self.get_exact_candle_data(date, '14:50'),
                    'programming_terms': {
                        'breakdown_confirmation': 'candle_low < channel_support',
                        'big_candle_validation': 'candle_range > average_candle_range * 1.5',
                        'IFC_definition': 'Inside_False_Close = body_closes_inside_but_wick_breaks',
                        'volume_confirmation': 'volume > average_volume * 1.2',
                        'follow_through': 'next_candle_continues_direction'
                    }
                }
            ],
            
            'key_levels': {
                'support_levels': [
                    {
                        'level': 'channel_bottom_calculated_from_range',
                        'significance': 'parallel_channel_support',
                        'test_times': ['multiple_touches_during_channel_formation']
                    }
                ],
                'resistance_levels': [
                    {
                        'level': 'channel_top_calculated_from_range',
                        'significance': 'parallel_channel_resistance',
                        'test_times': ['multiple_touches_during_channel_formation']
                    }
                ]
            }
        }
        
        return programming_analysis
    
    def map_jan_2_2018_analysis(self) -> Dict:
        """Map January 2, 2018 analysis to programming terms"""
        
        your_analysis = """So market formed the w previous day from the 3:00 to 3:25 then opened gapup around 45 points with inverted hanging man with long upper shadow and then market continues the bearish stance till 10:05 candle and with that candle and its next candle market forms the bullish engulfing and goes upside till 10:45. the 10:40 candle is similar to the 9:15 candle just somewhat small which shows the bearish sign the 9:40 candle to 10:25 candle market forms the small inverted head n shoulder then the retesting/ liquidity absrption took place till 11:20 candle and then went upside. The closing point of the 10:05 acted as the support and market took one tap support from that level at 2:15 candle. In terms of the supply, which came form 9:15 candle till 10:05 candle is the double supply of the 1:50 to the 2:15 candle and then at 2:35 market forms the proper Green Hammer and went upside till 3:00 same demand which formed from 10:10 candle to 10:45 candle and forms red spinning top candle at 3:05 showing indecisiveness, but in spinning top cases if the next 2 to 3 candles breaks the high or low of the spinning top with body close market goes in that direction and at the end market forms inverted hammer at 3:25. the wick of the candle formed on 1 jan 2018 3:20 acted as an the support Resistance interchange (SR interchange). It worked as the support on 9:50 candle and then breaks that on 10:00 and then again act as resistance on 10:15, breaks that on 10:20 again act as an support on 11:25 as well as 1:00 and then breaks that support on the 1:55 and then 2:05 candle breaks the resistance but leaves only the shadow didn't closes the body above SR interchange and the breaks the resistance on 2:45 and the next candle to it act as and support to the SR"""
        
        date = "2018-01-02"
        
        # Get Jan 1 close for gap calculation
        jan_1_data = self.df[self.df['datetime'].dt.date == pd.to_datetime('2018-01-01').date()]
        jan_1_close = jan_1_data.iloc[-1]['close'] if not jan_1_data.empty else None
        
        # Get Jan 1 3:20 candle for SR interchange
        jan_1_320_data = self.get_exact_candle_data('2018-01-01', '15:20')
        sr_interchange_level = jan_1_320_data['high']  # Using high as the wick level
        
        programming_analysis = {
            'observation_id': 'JAN_02_2018_PROGRAMMING',
            'date': date,
            'your_original_analysis': your_analysis,
            
            'market_structure': {
                'gap_analysis': {
                    'your_claim': 'opened gapup around 45 points',
                    'previous_close': jan_1_close,
                    'actual_open_data': self.get_exact_candle_data(date, '09:15'),
                    'programming_terms': {
                        'gap_calculation': f'open_price - {jan_1_close}',
                        'gap_validation': 'actual_gap ~= 45_points',
                        'gap_percentage': f'(gap_points / {jan_1_close}) * 100'
                    }
                },
                
                'w_pattern_reference': {
                    'your_claim': 'market formed the w previous day from the 3:00 to 3:25',
                    'reference_range': self.get_range_data('2018-01-01', '15:00', '15:25'),
                    'programming_terms': {
                        'w_pattern_validation': 'double_bottom_formation_in_timeframe',
                        'pattern_completion': 'second_low_higher_than_first_low',
                        'breakout_confirmation': 'price_breaks_above_middle_high'
                    }
                }
            },
            
            'session_phases': [
                {
                    'phase_name': 'opening_bearish_continuation',
                    'your_claim': 'market continues the bearish stance till 10:05 candle',
                    'time_range': {'start': '09:15', 'end': '10:05'},
                    'range_data': self.get_range_data(date, '09:15', '10:05'),
                    'programming_terms': {
                        'bearish_continuation': 'end_price < start_price',
                        'trend_strength': 'abs(price_decline) / start_price * 100',
                        'support_test': 'low_of_range_tests_previous_support'
                    }
                },
                {
                    'phase_name': 'bullish_reversal',
                    'your_claim': 'bullish engulfing and goes upside till 10:45',
                    'time_range': {'start': '10:05', 'end': '10:45'},
                    'range_data': self.get_range_data(date, '10:05', '10:45'),
                    'programming_terms': {
                        'engulfing_validation': 'second_candle_body_engulfs_first_completely',
                        'reversal_confirmation': 'end_price > start_price',
                        'momentum_strength': '(end_price - start_price) / start_price * 100'
                    }
                }
            ],
            
            'candlestick_patterns': [
                {
                    'pattern_name': 'opening_inverted_hanging_man',
                    'your_claim': 'opened with inverted hanging man with long upper shadow',
                    'time': '09:15',
                    'candle_data': self.get_exact_candle_data(date, '09:15'),
                    'programming_terms': {
                        'long_upper_shadow': 'upper_shadow > body_size * 3',
                        'pattern_significance': 'bearish_reversal_signal_after_gap_up',
                        'shadow_to_body_ratio': 'upper_shadow / body_size'
                    }
                },
                {
                    'pattern_name': 'bullish_engulfing',
                    'your_claim': 'market forms the bullish engulfing',
                    'time': '10:05-10:10',
                    'candle_data': {
                        'first_candle': self.get_exact_candle_data(date, '10:05'),
                        'second_candle': self.get_exact_candle_data(date, '10:10')
                    },
                    'programming_terms': {
                        'engulfing_validation': 'second_open < first_close and second_close > first_open',
                        'pattern_strength': 'second_body_size / first_body_size',
                        'reversal_confirmation': 'appears_at_support_level'
                    }
                },
                {
                    'pattern_name': 'green_hammer',
                    'your_claim': 'at 2:35 market forms the proper Green Hammer',
                    'time': '14:35',
                    'candle_data': self.get_exact_candle_data(date, '14:35'),
                    'programming_terms': {
                        'hammer_validation': 'lower_shadow > body_size * 2 and upper_shadow < body_size',
                        'color_confirmation': 'close > open (green)',
                        'support_test': 'candle_low_tests_support_level'
                    }
                },
                {
                    'pattern_name': 'red_spinning_top',
                    'your_claim': 'forms red spinning top candle at 3:05 showing indecisiveness',
                    'time': '15:05',
                    'candle_data': self.get_exact_candle_data(date, '15:05'),
                    'programming_terms': {
                        'spinning_top_validation': 'body_size < total_range * 0.3 and upper_shadow > body_size and lower_shadow > body_size',
                        'indecision_signal': 'equal_buying_and_selling_pressure',
                        'next_candle_rule': 'direction_determined_by_next_2_3_candles'
                    }
                },
                {
                    'pattern_name': 'final_inverted_hammer',
                    'your_claim': 'market forms inverted hammer at 3:25',
                    'time': '15:25',
                    'candle_data': self.get_exact_candle_data(date, '15:25'),
                    'programming_terms': {
                        'inverted_hammer_validation': 'upper_shadow > body_size * 2 and lower_shadow < body_size',
                        'reversal_potential': 'appears_after_decline',
                        'confirmation_needed': 'next_day_follow_through_required'
                    }
                }
            ],
            
            'chart_patterns': [
                {
                    'pattern_name': 'inverted_head_and_shoulders',
                    'your_claim': '9:40 candle to 10:25 candle market forms the small inverted head n shoulder',
                    'time_range': {'start': '09:40', 'end': '10:25'},
                    'range_data': self.get_range_data(date, '09:40', '10:25'),
                    'programming_terms': {
                        'left_shoulder': 'first_low_in_pattern',
                        'head': 'lowest_point_in_pattern',
                        'right_shoulder': 'third_low_higher_than_head',
                        'neckline': 'resistance_line_connecting_highs',
                        'breakout_target': 'neckline + (neckline - head_distance)'
                    }
                }
            ],
            
            'support_resistance_analysis': {
                'key_support_level': {
                    'your_claim': 'The closing point of the 10:05 acted as the support',
                    'level': self.get_exact_candle_data(date, '10:05')['close'],
                    'test_time': '14:15',
                    'test_data': self.get_exact_candle_data(date, '14:15'),
                    'programming_terms': {
                        'support_validation': 'candle_low_touches_level_and_bounces',
                        'support_strength': 'number_of_successful_tests',
                        'bounce_confirmation': 'close_above_support_level'
                    }
                },
                
                'sr_interchange': {
                    'your_claim': 'wick of candle formed on 1 jan 2018 3:20 acted as SR interchange',
                    'level': sr_interchange_level,
                    'test_times': ['09:50', '10:00', '10:15', '10:20', '11:25', '13:00', '13:55', '14:05', '14:45'],
                    'programming_terms': {
                        'sr_interchange_definition': 'level_acts_as_support_then_resistance_then_support',
                        'level_validation': 'multiple_touches_with_role_reversal',
                        'break_confirmation': 'body_close_above_or_below_level',
                        'false_break': 'wick_breaks_but_body_stays_inside'
                    }
                }
            },
            
            'supply_demand_analysis': {
                'supply_zones': [
                    {
                        'your_claim': 'supply from 9:15 candle till 10:05 candle is double supply of 1:50 to 2:15',
                        'primary_supply': self.get_range_data(date, '09:15', '10:05'),
                        'secondary_supply': self.get_range_data(date, '13:50', '14:15'),
                        'programming_terms': {
                            'supply_zone_validation': 'selling_pressure_creates_price_decline',
                            'double_supply_concept': 'second_supply_zone_half_the_strength',
                            'zone_effectiveness': 'price_reaction_at_zone_boundaries'
                        }
                    }
                ],
                'demand_zones': [
                    {
                        'your_claim': 'same demand which formed from 10:10 candle to 10:45 candle',
                        'demand_zone': self.get_range_data(date, '10:10', '10:45'),
                        'programming_terms': {
                            'demand_zone_validation': 'buying_pressure_creates_price_rise',
                            'zone_retest': 'price_returns_to_zone_and_bounces',
                            'demand_strength': 'speed_and_magnitude_of_price_rise'
                        }
                    }
                ]
            }
        }
        
        return programming_analysis
    
    def create_csv_mapping_structure(self, programming_analysis: Dict) -> Dict:
        """Create structure for filling CSV with programming terms"""
        
        csv_mapping = {
            'observation_id': programming_analysis['observation_id'],
            'date': programming_analysis['date'],
            
            'csv_fill_instructions': {
                'market_phases': [],
                'pattern_candles': [],
                'support_resistance_levels': [],
                'supply_demand_zones': []
            },
            
            'programming_formulas': {
                'gap_calculation': 'open_price - previous_close',
                'trend_direction': 'end_price - start_price',
                'trend_strength': 'abs(price_change) / start_price * 100',
                'candle_body': 'abs(close - open)',
                'upper_shadow': 'high - max(open, close)',
                'lower_shadow': 'min(open, close) - low',
                'pattern_validation': 'shadow_to_body_ratio_checks',
                'support_test': 'candle_low <= support_level <= candle_high',
                'resistance_test': 'candle_low <= resistance_level <= candle_high',
                'breakout_confirmation': 'body_close_beyond_level',
                'volume_confirmation': 'current_volume > average_volume * multiplier'
            }
        }
        
        # Add specific CSV filling instructions
        if 'session_phases' in programming_analysis['market_structure']:
            for phase in programming_analysis['market_structure']['session_phases']:
                if 'range_data' in phase and 'csv_indices' in phase['range_data']:
                    csv_mapping['csv_fill_instructions']['market_phases'].append({
                        'phase_name': phase['phase_name'],
                        'csv_rows': phase['range_data']['csv_indices'],
                        'fill_data': {
                            'Observation ID': f"{programming_analysis['observation_id']}_PHASE_{phase['phase_name'].upper()}",
                            'Market Condition': phase['phase_name'].replace('_', ' ').title(),
                            'Pattern Start Time': phase['time_range']['start'],
                            'Pattern End Time': phase['time_range']['end'],
                            'Your Reasoning': phase['your_claim'],
                            'Expected Outcome': f"Price movement: {phase['range_data']['range_stats']['net_move']:.2f} points"
                        }
                    })
        
        # Add candlestick patterns
        if 'candlestick_patterns' in programming_analysis:
            for pattern in programming_analysis['candlestick_patterns']:
                if 'candle_data' in pattern and 'csv_index' in pattern['candle_data']:
                    csv_mapping['csv_fill_instructions']['pattern_candles'].append({
                        'pattern_name': pattern['pattern_name'],
                        'csv_row': pattern['candle_data']['csv_index'],
                        'fill_data': {
                            'Observation ID': f"{programming_analysis['observation_id']}_PATTERN_{pattern['pattern_name'].upper()}",
                            'Pattern Name': pattern['pattern_name'].replace('_', ' ').title(),
                            'Pattern Start Time': pattern['time'],
                            'Pattern End Time': pattern['time'],
                            'Nature of Pattern': 'Reversal Signal',
                            'Your Reasoning': pattern['your_claim'],
                            'Expected Outcome': 'Pattern-based price reaction'
                        }
                    })
        
        return csv_mapping
    
    def generate_ai_training_code(self, programming_analysis: Dict) -> str:
        """Generate actual Python code for AI training"""
        
        code = f'''
# AI Training Code for {programming_analysis['observation_id']}
import pandas as pd
import numpy as np

class MarketAnalysisAI:
    def __init__(self, df):
        self.df = df
        
    def calculate_gap(self, current_open, previous_close):
        """Calculate gap based on your analysis method"""
        gap_points = current_open - previous_close
        gap_percentage = (gap_points / previous_close) * 100
        return {{
            'gap_points': gap_points,
            'gap_percentage': gap_percentage,
            'gap_type': 'gap_up' if gap_points > 0 else 'gap_down'
        }}
    
    def identify_trend_phase(self, start_price, end_price):
        """Identify trend phase based on your methodology"""
        move_points = end_price - start_price
        move_percentage = (move_points / start_price) * 100
        
        if abs(move_percentage) < 0.5:
            return 'sideways'
        elif move_points > 0:
            return 'bullish'
        else:
            return 'bearish'
    
    def validate_candlestick_pattern(self, candle_data, pattern_type):
        """Validate candlestick patterns using your criteria"""
        open_price = candle_data['open']
        high = candle_data['high']
        low = candle_data['low']
        close = candle_data['close']
        
        body_size = abs(close - open_price)
        upper_shadow = high - max(open_price, close)
        lower_shadow = min(open_price, close) - low
        total_range = high - low
        
        patterns = {{
            'inverted_hammer': upper_shadow > body_size * 2 and lower_shadow < body_size,
            'hanging_man': lower_shadow > body_size * 2 and upper_shadow < body_size,
            'spinning_top': body_size < total_range * 0.3 and upper_shadow > body_size and lower_shadow > body_size,
            'hammer': lower_shadow > body_size * 2 and upper_shadow < body_size and close > open_price
        }}
        
        return patterns.get(pattern_type, False)
    
    def identify_support_resistance(self, price_level, candle_data, role='support'):
        """Identify support/resistance based on your SR interchange concept"""
        candle_high = candle_data['high']
        candle_low = candle_data['low']
        candle_close = candle_data['close']
        
        level_tested = candle_low <= price_level <= candle_high
        
        if role == 'support':
            successful_test = level_tested and candle_close > price_level
        else:  # resistance
            successful_test = level_tested and candle_close < price_level
            
        return {{
            'level_tested': level_tested,
            'successful_test': successful_test,
            'break_type': 'body_break' if abs(candle_close - price_level) > 2 else 'wick_only'
        }}
    
    def analyze_supply_demand_zones(self, zone_start, zone_end, zone_type='supply'):
        """Analyze supply/demand zones based on your methodology"""
        zone_range = abs(zone_end - zone_start)
        zone_midpoint = (zone_start + zone_end) / 2
        
        return {{
            'zone_range': zone_range,
            'zone_midpoint': zone_midpoint,
            'zone_strength': 'strong' if zone_range > 20 else 'weak',
            'zone_type': zone_type
        }}

# Your specific analysis patterns converted to code
def apply_your_methodology(df, date):
    """Apply your specific analysis methodology"""
    analyzer = MarketAnalysisAI(df)
    
    # Your gap analysis
    day_data = df[df['datetime'].dt.date == pd.to_datetime(date).date()]
    if not day_data.empty:
        # Gap calculation as per your method
        gap_analysis = analyzer.calculate_gap(
            day_data.iloc[0]['open'], 
            previous_close  # Your specified previous close
        )
        
        # Phase analysis as per your observations
        phases = []
        # Add your specific phase analysis here
        
        # Pattern analysis as per your identification
        patterns = []
        # Add your specific pattern analysis here
        
        return {{
            'gap_analysis': gap_analysis,
            'phases': phases,
            'patterns': patterns
        }}
'''
        
        return code

def main():
    """Main function to map analysis to programming terms"""
    
    print("🔄 MAPPING YOUR ANALYSIS TO PROGRAMMING TERMS")
    print("=" * 60)
    
    mapper = AnalysisToProgrammingMapper()
    
    # Map both days
    jan_1_programming = mapper.map_jan_1_2018_analysis()
    jan_2_programming = mapper.map_jan_2_2018_analysis()
    
    # Create CSV mapping structures
    jan_1_csv_mapping = mapper.create_csv_mapping_structure(jan_1_programming)
    jan_2_csv_mapping = mapper.create_csv_mapping_structure(jan_2_programming)
    
    # Generate AI training code
    jan_1_code = mapper.generate_ai_training_code(jan_1_programming)
    jan_2_code = mapper.generate_ai_training_code(jan_2_programming)
    
    # Combine all results
    complete_mapping = {
        'metadata': {
            'created_at': datetime.now().isoformat(),
            'purpose': 'Convert natural language analysis to programming terms',
            'approach': 'Treat user analysis as ground truth and map to CSV data'
        },
        'jan_1_2018': {
            'programming_analysis': jan_1_programming,
            'csv_mapping': jan_1_csv_mapping,
            'ai_code': jan_1_code
        },
        'jan_2_2018': {
            'programming_analysis': jan_2_programming,
            'csv_mapping': jan_2_csv_mapping,
            'ai_code': jan_2_code
        }
    }
    
    # Save results
    with open('analysis_programming_mapping.json', 'w') as f:
        json.dump(complete_mapping, f, indent=2, default=str)
    
    print("✅ Analysis mapped to programming terms")
    print(f"📊 Jan 1: {len(jan_1_programming['candlestick_patterns'])} patterns, {len(jan_1_programming['market_structure']['session_phases'])} phases")
    print(f"📊 Jan 2: {len(jan_2_programming['candlestick_patterns'])} patterns, {len(jan_2_programming['market_structure']['session_phases'])} phases")
    print("💾 Saved to 'analysis_programming_mapping.json'")
    
    # Show sample programming terms
    print(f"\n🤖 SAMPLE PROGRAMMING TERMS:")
    print("Gap Analysis:", jan_1_programming['market_structure']['gap_analysis']['programming_terms'])
    print("Pattern Validation:", jan_1_programming['candlestick_patterns'][0]['programming_terms'])
    
    return complete_mapping

if __name__ == "__main__":
    results = main()
