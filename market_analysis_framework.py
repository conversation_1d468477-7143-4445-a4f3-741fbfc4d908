"""
Market Self-Analysis AI Framework
This framework helps analyze market data and generate insights with human observations
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class MarketAnalysisAI:
    def __init__(self, data_path: str = "."):
        """Initialize the Market Analysis AI with OHLC data"""
        self.data_path = data_path
        self.indices_data = {}
        self.analysis_history = []
        self.load_data()
        
    def load_data(self):
        """Load all OHLC data files"""
        indices = ['Nifty', 'Banknifty', 'Finnifty', 'Midcap', 'Sensex']
        
        for index in indices:
            try:
                df = pd.read_csv(f"{self.data_path}/{index}.csv")
                df['datetime'] = pd.to_datetime(df['datetime'])
                df.set_index('datetime', inplace=True)
                self.indices_data[index] = df
                print(f"Loaded {index}: {len(df)} records from {df.index.min()} to {df.index.max()}")
            except Exception as e:
                print(f"Error loading {index}: {e}")
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate essential technical indicators"""
        # Moving Averages
        df['SMA_20'] = df['close'].rolling(window=20).mean()
        df['SMA_50'] = df['close'].rolling(window=50).mean()
        df['EMA_20'] = df['close'].ewm(span=20).mean()
        
        # RSI
        delta = df['close'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        df['RSI'] = 100 - (100 / (1 + rs))
        
        # Bollinger Bands
        df['BB_middle'] = df['close'].rolling(window=20).mean()
        bb_std = df['close'].rolling(window=20).std()
        df['BB_upper'] = df['BB_middle'] + (bb_std * 2)
        df['BB_lower'] = df['BB_middle'] - (bb_std * 2)
        
        # MACD
        exp1 = df['close'].ewm(span=12).mean()
        exp2 = df['close'].ewm(span=26).mean()
        df['MACD'] = exp1 - exp2
        df['MACD_signal'] = df['MACD'].ewm(span=9).mean()
        
        # Support and Resistance levels
        df['resistance'] = df['high'].rolling(window=20).max()
        df['support'] = df['low'].rolling(window=20).min()
        
        return df
    
    def identify_key_levels(self, df: pd.DataFrame, timeframe: str = "1D") -> Dict:
        """Identify key support/resistance levels and pivot points"""
        # Resample to daily if needed
        if timeframe == "1D":
            daily_df = df.resample('1D').agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last'
            }).dropna()
        else:
            daily_df = df
            
        # Find swing highs and lows
        swing_highs = []
        swing_lows = []
        
        for i in range(2, len(daily_df) - 2):
            # Swing High: current high > previous 2 and next 2 highs
            if (daily_df.iloc[i]['high'] > daily_df.iloc[i-1]['high'] and 
                daily_df.iloc[i]['high'] > daily_df.iloc[i-2]['high'] and
                daily_df.iloc[i]['high'] > daily_df.iloc[i+1]['high'] and 
                daily_df.iloc[i]['high'] > daily_df.iloc[i+2]['high']):
                swing_highs.append({
                    'datetime': daily_df.index[i],
                    'price': daily_df.iloc[i]['high'],
                    'type': 'swing_high'
                })
            
            # Swing Low: current low < previous 2 and next 2 lows
            if (daily_df.iloc[i]['low'] < daily_df.iloc[i-1]['low'] and 
                daily_df.iloc[i]['low'] < daily_df.iloc[i-2]['low'] and
                daily_df.iloc[i]['low'] < daily_df.iloc[i+1]['low'] and 
                daily_df.iloc[i]['low'] < daily_df.iloc[i+2]['low']):
                swing_lows.append({
                    'datetime': daily_df.index[i],
                    'price': daily_df.iloc[i]['low'],
                    'type': 'swing_low'
                })
        
        return {
            'swing_highs': swing_highs[-10:],  # Last 10 swing highs
            'swing_lows': swing_lows[-10:],    # Last 10 swing lows
            'current_resistance': daily_df['high'].rolling(window=20).max().iloc[-1],
            'current_support': daily_df['low'].rolling(window=20).min().iloc[-1]
        }
    
    def analyze_market_structure(self, index_name: str, start_date: str = None, end_date: str = None) -> Dict:
        """Analyze market structure for a specific timeframe"""
        df = self.indices_data[index_name].copy()
        
        if start_date:
            df = df[df.index >= start_date]
        if end_date:
            df = df[df.index <= end_date]
            
        # Calculate technical indicators
        df = self.calculate_technical_indicators(df)
        
        # Get key levels
        key_levels = self.identify_key_levels(df)
        
        # Market trend analysis
        current_price = df['close'].iloc[-1]
        sma_20 = df['SMA_20'].iloc[-1]
        sma_50 = df['SMA_50'].iloc[-1]
        
        trend = "Neutral"
        if current_price > sma_20 > sma_50:
            trend = "Bullish"
        elif current_price < sma_20 < sma_50:
            trend = "Bearish"
            
        # Recent price action
        recent_high = df['high'].tail(20).max()
        recent_low = df['low'].tail(20).min()
        price_range = recent_high - recent_low
        
        analysis = {
            'index': index_name,
            'timeframe': f"{df.index.min()} to {df.index.max()}",
            'current_price': current_price,
            'trend': trend,
            'key_levels': key_levels,
            'technical_indicators': {
                'RSI': df['RSI'].iloc[-1],
                'MACD': df['MACD'].iloc[-1],
                'MACD_signal': df['MACD_signal'].iloc[-1],
                'SMA_20': sma_20,
                'SMA_50': sma_50
            },
            'recent_range': {
                'high': recent_high,
                'low': recent_low,
                'range_percent': (price_range / current_price) * 100
            }
        }
        
        return analysis
    
    def add_human_observation(self, observation: Dict):
        """Add human analysis and observations to the system"""
        observation['timestamp'] = datetime.now().isoformat()
        observation['id'] = len(self.analysis_history) + 1
        self.analysis_history.append(observation)
        
        # Save to file
        with open('analysis_history.json', 'w') as f:
            json.dump(self.analysis_history, f, indent=2, default=str)
    
    def create_observation_template(self, index_name: str, specific_datetime: str = None) -> Dict:
        """Create a template for human observations"""
        if specific_datetime:
            target_time = pd.to_datetime(specific_datetime)
            df = self.indices_data[index_name]
            closest_data = df.iloc[df.index.get_indexer([target_time], method='nearest')[0]]
            
            template = {
                'index': index_name,
                'analysis_datetime': specific_datetime,
                'price_data': {
                    'open': closest_data['open'],
                    'high': closest_data['high'],
                    'low': closest_data['low'],
                    'close': closest_data['close']
                },
                'observation': "",  # Your analysis here
                'key_points': [],   # Bullet points of key observations
                'market_context': "",  # Overall market condition
                'technical_analysis': "",  # Technical patterns observed
                'price_action': "",  # Specific price movements
                'support_resistance': {
                    'support_levels': [],
                    'resistance_levels': []
                },
                'prediction': "",  # Your market prediction
                'confidence_level': "",  # High/Medium/Low
                'risk_factors': []  # Potential risks identified
            }
        else:
            # Current market template
            current_analysis = self.analyze_market_structure(index_name)
            template = {
                'index': index_name,
                'analysis_datetime': datetime.now().isoformat(),
                'current_market_data': current_analysis,
                'observation': "",
                'key_points': [],
                'market_context': "",
                'technical_analysis': "",
                'price_action': "",
                'support_resistance': current_analysis['key_levels'],
                'prediction': "",
                'confidence_level': "",
                'risk_factors': []
            }
            
        return template

# Example usage and data requirements guide
if __name__ == "__main__":
    # Initialize the analysis system
    analyzer = MarketAnalysisAI()
    
    # Example: Analyze current Nifty structure
    nifty_analysis = analyzer.analyze_market_structure('Nifty')
    print("Current Nifty Analysis:")
    print(json.dumps(nifty_analysis, indent=2, default=str))
    
    # Create observation template for specific time
    observation_template = analyzer.create_observation_template('Nifty', '2025-08-08 15:25:00')
    print("\nObservation Template:")
    print(json.dumps(observation_template, indent=2, default=str))
