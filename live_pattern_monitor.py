"""
Live Pattern Monitor
Integrates your personal pattern observations with live market data
for real-time analysis and alerts based on your documented patterns
"""

import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import time

class LivePatternMonitor:
    def __init__(self, patterns_file: str = "personal_patterns.json"):
        self.patterns_file = patterns_file
        self.active_patterns = []
        self.pattern_library = self.load_pattern_library()
        self.alerts = []
        
    def load_pattern_library(self) -> List[Dict]:
        """Load your documented patterns for reference"""
        try:
            with open(self.patterns_file, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Pattern file {self.patterns_file} not found. Starting with empty library.")
            return []
    
    def add_live_pattern(self, pattern_observation: Dict):
        """Add a pattern for live monitoring"""
        live_pattern = {
            'id': pattern_observation['id'],
            'pattern_name': pattern_observation['pattern_name'],
            'index': pattern_observation['index'],
            'created_at': datetime.now().isoformat(),
            'status': 'active',
            
            # Monitoring parameters from your observation
            'monitoring_setup': {
                'breakout_level': pattern_observation['precise_references'].get('breakout_level'),
                'support_levels': [ref['price_level'] for ref in pattern_observation['precise_references']['price_levels'] 
                                 if 'support' in ref['significance'].lower()],
                'resistance_levels': [ref['price_level'] for ref in pattern_observation['precise_references']['price_levels'] 
                                    if 'resistance' in ref['significance'].lower()],
                'target_levels': pattern_observation['prediction']['target_levels'],
                'stop_loss': pattern_observation['prediction']['stop_loss']
            },
            
            # Your analysis for AI reference
            'analysis_context': {
                'pattern_description': pattern_observation['personal_analysis']['observation_text'],
                'expected_outcome': pattern_observation['prediction']['expected_outcome'],
                'confidence': pattern_observation['prediction']['confidence'],
                'timeframe': pattern_observation['prediction']['timeframe']
            },
            
            # Alert conditions based on your analysis
            'alert_conditions': self.create_alert_conditions(pattern_observation),
            
            # Tracking
            'alerts_triggered': [],
            'pattern_progress': 'forming',  # forming, confirmed, completed, failed
            'last_checked': None
        }
        
        self.active_patterns.append(live_pattern)
        return live_pattern
    
    def create_alert_conditions(self, pattern_obs: Dict) -> List[Dict]:
        """Create alert conditions based on your pattern analysis"""
        conditions = []
        
        # Breakout alert
        if pattern_obs['precise_references'].get('breakout_level'):
            conditions.append({
                'type': 'breakout',
                'condition': f"price > {pattern_obs['precise_references']['breakout_level']}",
                'message': f"Breakout confirmed above {pattern_obs['precise_references']['breakout_level']} as predicted",
                'priority': 'high'
            })
        
        # Target alerts
        for i, target in enumerate(pattern_obs['prediction']['target_levels']):
            conditions.append({
                'type': 'target',
                'condition': f"price >= {target}",
                'message': f"Target {i+1} reached at {target}",
                'priority': 'medium'
            })
        
        # Stop loss alert
        if pattern_obs['prediction']['stop_loss']:
            conditions.append({
                'type': 'stop_loss',
                'condition': f"price <= {pattern_obs['prediction']['stop_loss']}",
                'message': f"Stop loss triggered at {pattern_obs['prediction']['stop_loss']}",
                'priority': 'high'
            })
        
        # Support/Resistance tests
        for price_ref in pattern_obs['precise_references']['price_levels']:
            if 'support' in price_ref['significance'].lower():
                conditions.append({
                    'type': 'support_test',
                    'condition': f"price <= {price_ref['price_level'] * 1.002}",  # Small buffer
                    'message': f"Testing support at {price_ref['price_level']} - {price_ref['significance']}",
                    'priority': 'medium'
                })
            elif 'resistance' in price_ref['significance'].lower():
                conditions.append({
                    'type': 'resistance_test',
                    'condition': f"price >= {price_ref['price_level'] * 0.998}",  # Small buffer
                    'message': f"Testing resistance at {price_ref['price_level']} - {price_ref['significance']}",
                    'priority': 'medium'
                })
        
        return conditions
    
    def check_pattern_status(self, pattern: Dict, current_price: float, current_time: str) -> List[Dict]:
        """Check if any alerts should be triggered for a pattern"""
        triggered_alerts = []
        
        for condition in pattern['alert_conditions']:
            # Simple condition evaluation (in real implementation, use proper expression parser)
            condition_met = False
            
            if 'price >' in condition['condition']:
                threshold = float(condition['condition'].split('>')[-1].strip())
                condition_met = current_price > threshold
            elif 'price >=' in condition['condition']:
                threshold = float(condition['condition'].split('>= ')[-1].strip())
                condition_met = current_price >= threshold
            elif 'price <=' in condition['condition']:
                threshold = float(condition['condition'].split('<= ')[-1].strip())
                condition_met = current_price <= threshold
            elif 'price <' in condition['condition']:
                threshold = float(condition['condition'].split('<')[-1].strip())
                condition_met = current_price < threshold
            
            if condition_met:
                alert = {
                    'pattern_id': pattern['id'],
                    'pattern_name': pattern['pattern_name'],
                    'index': pattern['index'],
                    'alert_type': condition['type'],
                    'message': condition['message'],
                    'priority': condition['priority'],
                    'price': current_price,
                    'time': current_time,
                    'original_analysis': pattern['analysis_context']['pattern_description']
                }
                triggered_alerts.append(alert)
                pattern['alerts_triggered'].append(alert)
        
        pattern['last_checked'] = current_time
        return triggered_alerts
    
    def simulate_live_monitoring(self, index: str, price_data: pd.DataFrame):
        """Simulate live monitoring with historical data"""
        print(f"\n=== Simulating Live Monitoring for {index} ===")
        
        # Get active patterns for this index
        index_patterns = [p for p in self.active_patterns if p['index'] == index]
        
        if not index_patterns:
            print(f"No active patterns for {index}")
            return
        
        print(f"Monitoring {len(index_patterns)} active patterns...")
        
        # Simulate real-time price updates
        for i, row in price_data.tail(100).iterrows():  # Last 100 data points
            current_price = row['close']
            current_time = str(row.name) if hasattr(row.name, 'strftime') else str(i)
            
            for pattern in index_patterns:
                alerts = self.check_pattern_status(pattern, current_price, current_time)
                
                for alert in alerts:
                    self.alerts.append(alert)
                    print(f"\n🚨 ALERT: {alert['alert_type'].upper()}")
                    print(f"Pattern: {alert['pattern_name']}")
                    print(f"Message: {alert['message']}")
                    print(f"Price: {alert['price']} at {alert['time']}")
                    print(f"Original Analysis: {alert['original_analysis'][:100]}...")
    
    def generate_pattern_performance_report(self) -> Dict:
        """Generate performance report of your patterns"""
        report = {
            'total_patterns_monitored': len(self.active_patterns),
            'total_alerts_generated': len(self.alerts),
            'pattern_success_rate': {},
            'alert_breakdown': {},
            'most_successful_patterns': [],
            'pattern_insights': []
        }
        
        # Alert type breakdown
        alert_types = {}
        for alert in self.alerts:
            alert_types[alert['alert_type']] = alert_types.get(alert['alert_type'], 0) + 1
        report['alert_breakdown'] = alert_types
        
        # Pattern performance
        pattern_performance = {}
        for pattern in self.active_patterns:
            pattern_name = pattern['pattern_name']
            alerts_count = len(pattern['alerts_triggered'])
            
            # Count successful alerts (targets hit)
            successful_alerts = len([a for a in pattern['alerts_triggered'] if a['alert_type'] == 'target'])
            
            pattern_performance[pattern_name] = {
                'total_alerts': alerts_count,
                'successful_alerts': successful_alerts,
                'success_rate': (successful_alerts / alerts_count * 100) if alerts_count > 0 else 0
            }
        
        report['pattern_success_rate'] = pattern_performance
        
        # Most successful patterns
        sorted_patterns = sorted(pattern_performance.items(), 
                               key=lambda x: x[1]['success_rate'], reverse=True)
        report['most_successful_patterns'] = sorted_patterns[:5]
        
        return report
    
    def create_ai_learning_dataset(self) -> Dict:
        """Create dataset for AI to learn from your pattern observations"""
        learning_data = {
            'successful_patterns': [],
            'failed_patterns': [],
            'pattern_features': [],
            'market_conditions': [],
            'your_analysis_style': []
        }
        
        for pattern in self.active_patterns:
            # Determine if pattern was successful
            target_alerts = [a for a in pattern['alerts_triggered'] if a['alert_type'] == 'target']
            stop_alerts = [a for a in pattern['alerts_triggered'] if a['alert_type'] == 'stop_loss']
            
            pattern_data = {
                'pattern_name': pattern['pattern_name'],
                'index': pattern['index'],
                'analysis_text': pattern['analysis_context']['pattern_description'],
                'expected_outcome': pattern['analysis_context']['expected_outcome'],
                'confidence': pattern['analysis_context']['confidence'],
                'monitoring_levels': pattern['monitoring_setup'],
                'alerts_triggered': len(pattern['alerts_triggered']),
                'targets_hit': len(target_alerts),
                'stopped_out': len(stop_alerts) > 0
            }
            
            if target_alerts and not stop_alerts:
                learning_data['successful_patterns'].append(pattern_data)
            elif stop_alerts:
                learning_data['failed_patterns'].append(pattern_data)
            
            # Extract your analysis style
            learning_data['your_analysis_style'].append({
                'pattern_description_style': pattern['analysis_context']['pattern_description'],
                'confidence_expression': pattern['analysis_context']['confidence'],
                'prediction_style': pattern['analysis_context']['expected_outcome']
            })
        
        return learning_data
    
    def export_for_ai_training(self, filename: str = "ai_training_data.json"):
        """Export all data for AI training"""
        training_data = {
            'pattern_library': self.pattern_library,
            'live_monitoring_results': self.create_ai_learning_dataset(),
            'performance_metrics': self.generate_pattern_performance_report(),
            'alert_history': self.alerts,
            'metadata': {
                'total_patterns': len(self.pattern_library),
                'monitoring_period': f"{datetime.now().isoformat()}",
                'analyst_style': 'Natural Language Pattern Recognition'
            }
        }
        
        with open(filename, 'w') as f:
            json.dump(training_data, f, indent=2, default=str)
        
        print(f"AI training data exported to {filename}")
        return training_data

# Example usage
if __name__ == "__main__":
    # Initialize monitor
    monitor = LivePatternMonitor()
    
    # Example: Add a pattern for monitoring (you would create this from your observations)
    example_pattern = {
        'id': 1,
        'pattern_name': 'Ellipse Pennant',
        'index': 'Nifty',
        'personal_analysis': {
            'observation_text': 'Classic ellipse pennant formation with converging trend lines'
        },
        'precise_references': {
            'breakout_level': 24365,
            'price_levels': [
                {'price_level': 24320, 'significance': 'Strong support level'},
                {'price_level': 24380, 'significance': 'Key resistance level'}
            ]
        },
        'prediction': {
            'expected_outcome': 'Upward breakout to 24420-24450',
            'target_levels': [24420, 24450],
            'stop_loss': 24300,
            'confidence': 'High',
            'timeframe': '2-3 hours'
        }
    }
    
    # Add pattern for live monitoring
    live_pattern = monitor.add_live_pattern(example_pattern)
    print(f"Added pattern for monitoring: {live_pattern['pattern_name']}")
    print(f"Monitoring {len(live_pattern['alert_conditions'])} alert conditions")
    
    # Simulate with sample data (you would use real live data)
    sample_prices = pd.DataFrame({
        'close': [24350, 24360, 24370, 24380, 24375, 24420, 24435]
    })
    
    # Run simulation
    monitor.simulate_live_monitoring('Nifty', sample_prices)
    
    # Generate reports
    performance_report = monitor.generate_pattern_performance_report()
    print(f"\nPerformance Report:")
    print(f"Total alerts: {performance_report['total_alerts_generated']}")
    print(f"Alert types: {performance_report['alert_breakdown']}")
    
    # Export for AI training
    monitor.export_for_ai_training()
