"""
Personal Pattern Analysis Recorder
A system to capture your unique market observations in natural language
with precise time and price references for AI training
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Optional
import os

class PersonalPatternRecorder:
    def __init__(self, data_path: str = "."):
        self.data_path = data_path
        self.patterns_file = "personal_patterns.json"
        self.patterns = self.load_patterns()
        self.indices_data = self.load_market_data()
        
    def load_market_data(self):
        """Load OHLC data for price context"""
        indices_data = {}
        indices = ['Nifty', 'Banknifty', 'Finnifty', 'Midcap', 'Sensex']
        
        for index in indices:
            try:
                df = pd.read_csv(f"{self.data_path}/{index}.csv")
                df['datetime'] = pd.to_datetime(df['datetime'])
                indices_data[index] = df
            except Exception as e:
                print(f"Could not load {index}: {e}")
        
        return indices_data
        
    def load_patterns(self) -> List[Dict]:
        """Load existing pattern observations"""
        if os.path.exists(self.patterns_file):
            with open(self.patterns_file, 'r') as f:
                return json.load(f)
        return []
    
    def save_patterns(self):
        """Save patterns to file"""
        with open(self.patterns_file, 'w') as f:
            json.dump(self.patterns, f, indent=2, default=str)
    
    def get_price_context(self, index: str, start_time: str, end_time: str = None) -> Dict:
        """Get price data for the pattern timeframe"""
        if index not in self.indices_data:
            return {}
            
        df = self.indices_data[index]
        start_dt = pd.to_datetime(start_time)
        
        if end_time:
            end_dt = pd.to_datetime(end_time)
            mask = (df['datetime'] >= start_dt) & (df['datetime'] <= end_dt)
        else:
            # Get closest single point
            closest_idx = df['datetime'].sub(start_dt).abs().idxmin()
            return {
                'datetime': str(df.iloc[closest_idx]['datetime']),
                'open': df.iloc[closest_idx]['open'],
                'high': df.iloc[closest_idx]['high'],
                'low': df.iloc[closest_idx]['low'],
                'close': df.iloc[closest_idx]['close']
            }
        
        pattern_data = df[mask].copy()
        if pattern_data.empty:
            return {}
            
        return {
            'start_datetime': str(pattern_data.iloc[0]['datetime']),
            'end_datetime': str(pattern_data.iloc[-1]['datetime']),
            'pattern_high': pattern_data['high'].max(),
            'pattern_low': pattern_data['low'].min(),
            'start_price': pattern_data.iloc[0]['close'],
            'end_price': pattern_data.iloc[-1]['close'],
            'price_range': pattern_data['high'].max() - pattern_data['low'].min(),
            'candle_count': len(pattern_data)
        }
    
    def create_pattern_observation(self) -> Dict:
        """Interactive creation of pattern observation"""
        print("\n=== Personal Pattern Analysis Entry ===")
        
        # Basic info
        index = input("Index (Nifty/Banknifty/Finnifty/Midcap/Sensex): ").strip()
        pattern_name = input("Pattern Name (e.g., 'Ellipse Pennant', 'Double Bottom'): ").strip()
        
        # Time references
        print("\n--- Pattern Timeline ---")
        start_time = input("Pattern Start Time (YYYY-MM-DD HH:MM): ").strip()
        end_time = input("Pattern End Time (YYYY-MM-DD HH:MM, or Enter for single point): ").strip()
        
        # Get price context
        if end_time:
            price_context = self.get_price_context(index, start_time, end_time)
        else:
            price_context = self.get_price_context(index, start_time)
        
        print(f"\nPrice Context Retrieved:")
        if 'pattern_high' in price_context:
            print(f"Pattern High: {price_context['pattern_high']}")
            print(f"Pattern Low: {price_context['pattern_low']}")
            print(f"Price Range: {price_context['price_range']:.2f}")
        else:
            print(f"Price at {start_time}: {price_context.get('close', 'N/A')}")
        
        # Your personal analysis
        print("\n--- Your Analysis (in your own words) ---")
        analysis_text = input("Describe what you observed: ").strip()
        
        # Specific references
        print("\n--- Specific References ---")
        key_times = input("Key times in pattern (comma-separated, YYYY-MM-DD HH:MM): ").strip()
        key_prices = input("Key price levels (comma-separated): ").strip()
        
        # Pattern details
        print("\n--- Pattern Details ---")
        formation_desc = input("How did this pattern form? ").strip()
        breakout_level = input("Breakout level (if any): ").strip()
        
        # Prediction
        print("\n--- Your Prediction ---")
        prediction = input("What do you expect to happen? ").strip()
        target_levels = input("Target levels (comma-separated): ").strip()
        stop_loss = input("Stop loss level: ").strip()
        timeframe = input("Expected timeframe: ").strip()
        confidence = input("Confidence (High/Medium/Low): ").strip() or "Medium"
        
        # Process inputs
        time_refs = []
        if key_times:
            for time_str in key_times.split(','):
                time_str = time_str.strip()
                if time_str:
                    time_context = self.get_price_context(index, time_str)
                    time_refs.append({
                        'datetime': time_str,
                        'price_at_time': time_context.get('close'),
                        'significance': input(f"Why is {time_str} important? ").strip()
                    })
        
        price_refs = []
        if key_prices:
            for price_str in key_prices.split(','):
                price_str = price_str.strip()
                if price_str:
                    try:
                        price_val = float(price_str)
                        price_refs.append({
                            'price_level': price_val,
                            'significance': input(f"Why is {price_val} important? ").strip()
                        })
                    except ValueError:
                        continue
        
        # Create structured observation
        observation = {
            'id': len(self.patterns) + 1,
            'created_at': datetime.now().isoformat(),
            'analyst': "Personal Analysis",
            'index': index,
            'pattern_name': pattern_name,
            
            # Timeline
            'pattern_timeline': {
                'start_time': start_time,
                'end_time': end_time if end_time else start_time,
                'analysis_date': datetime.now().strftime('%Y-%m-%d')
            },
            
            # Price context from data
            'price_context': price_context,
            
            # Your personal analysis
            'personal_analysis': {
                'observation_text': analysis_text,
                'pattern_formation': formation_desc,
                'confidence_level': confidence
            },
            
            # Specific references for AI training
            'precise_references': {
                'time_points': time_refs,
                'price_levels': price_refs,
                'breakout_level': float(breakout_level) if breakout_level else None
            },
            
            # Your prediction framework
            'prediction': {
                'expected_outcome': prediction,
                'target_levels': [float(x.strip()) for x in target_levels.split(',') if x.strip()] if target_levels else [],
                'stop_loss': float(stop_loss) if stop_loss else None,
                'timeframe': timeframe,
                'confidence': confidence
            },
            
            # For AI learning and validation
            'ai_training_data': {
                'pattern_type': pattern_name,
                'market_structure': analysis_text,
                'key_levels_identified': price_refs,
                'time_sequence': time_refs,
                'prediction_logic': prediction
            },
            
            # Outcome tracking
            'outcome': {
                'actual_result': "",
                'accuracy_score': None,
                'pattern_worked': None,
                'lessons_learned': "",
                'ai_insights': ""
            }
        }
        
        return observation
    
    def add_pattern(self, pattern: Dict):
        """Add pattern to database"""
        self.patterns.append(pattern)
        self.save_patterns()
        print(f"\nPattern #{pattern['id']} '{pattern['pattern_name']}' saved successfully!")
    
    def update_pattern_outcome(self, pattern_id: int):
        """Update pattern outcome for AI learning"""
        pattern = next((p for p in self.patterns if p['id'] == pattern_id), None)
        if not pattern:
            print(f"Pattern #{pattern_id} not found!")
            return
        
        print(f"\n=== Updating Outcome for Pattern #{pattern_id} ===")
        print(f"Pattern: {pattern['pattern_name']}")
        print(f"Original Prediction: {pattern['prediction']['expected_outcome']}")
        
        actual_result = input("What actually happened? ").strip()
        worked = input("Did the pattern work as expected? (Yes/No/Partial): ").strip()
        accuracy = input("Accuracy score (1-10): ").strip()
        lessons = input("What did you learn from this? ").strip()
        ai_insights = input("What should AI learn from this pattern? ").strip()
        
        pattern['outcome'].update({
            'actual_result': actual_result,
            'pattern_worked': worked,
            'accuracy_score': int(accuracy) if accuracy.isdigit() else None,
            'lessons_learned': lessons,
            'ai_insights': ai_insights,
            'outcome_updated': datetime.now().isoformat()
        })
        
        self.save_patterns()
        print("Outcome updated successfully!")
    
    def search_patterns(self, pattern_name: str = None, index: str = None) -> List[Dict]:
        """Search patterns by criteria"""
        results = self.patterns.copy()
        
        if pattern_name:
            results = [p for p in results if pattern_name.lower() in p['pattern_name'].lower()]
        
        if index:
            results = [p for p in results if p['index'].lower() == index.lower()]
        
        return results
    
    def generate_ai_training_dataset(self) -> Dict:
        """Generate dataset for AI training"""
        training_data = {
            'patterns': [],
            'successful_patterns': [],
            'failed_patterns': [],
            'pattern_types': {},
            'key_insights': []
        }
        
        for pattern in self.patterns:
            # Basic pattern data for AI
            ai_pattern = {
                'pattern_name': pattern['pattern_name'],
                'index': pattern['index'],
                'price_context': pattern['price_context'],
                'analysis_text': pattern['personal_analysis']['observation_text'],
                'key_levels': pattern['precise_references']['price_levels'],
                'time_sequence': pattern['precise_references']['time_points'],
                'prediction': pattern['prediction']['expected_outcome'],
                'outcome': pattern['outcome']['actual_result']
            }
            
            training_data['patterns'].append(ai_pattern)
            
            # Categorize by success
            if pattern['outcome'].get('pattern_worked') == 'Yes':
                training_data['successful_patterns'].append(ai_pattern)
            elif pattern['outcome'].get('pattern_worked') == 'No':
                training_data['failed_patterns'].append(ai_pattern)
            
            # Pattern type frequency
            ptype = pattern['pattern_name']
            training_data['pattern_types'][ptype] = training_data['pattern_types'].get(ptype, 0) + 1
            
            # AI insights
            if pattern['outcome'].get('ai_insights'):
                training_data['key_insights'].append({
                    'pattern': ptype,
                    'insight': pattern['outcome']['ai_insights']
                })
        
        return training_data

# Interactive CLI
if __name__ == "__main__":
    recorder = PersonalPatternRecorder()
    
    while True:
        print("\n=== Personal Pattern Analysis System ===")
        print("1. Record New Pattern Observation")
        print("2. View Recent Patterns")
        print("3. Update Pattern Outcome")
        print("4. Search Patterns")
        print("5. Generate AI Training Data")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == "1":
            pattern = recorder.create_pattern_observation()
            recorder.add_pattern(pattern)
        
        elif choice == "2":
            recent = recorder.patterns[-5:] if recorder.patterns else []
            print(f"\nLast {len(recent)} patterns:")
            for p in recent:
                print(f"#{p['id']}: {p['pattern_name']} on {p['index']} - {p['pattern_timeline']['start_time']}")
        
        elif choice == "3":
            pattern_id = int(input("Pattern ID to update: "))
            recorder.update_pattern_outcome(pattern_id)
        
        elif choice == "4":
            pattern_name = input("Pattern name to search (or Enter for all): ").strip() or None
            index = input("Index to search (or Enter for all): ").strip() or None
            results = recorder.search_patterns(pattern_name, index)
            print(f"\nFound {len(results)} patterns:")
            for p in results[:10]:
                print(f"#{p['id']}: {p['pattern_name']} on {p['index']}")
        
        elif choice == "5":
            training_data = recorder.generate_ai_training_dataset()
            with open('ai_training_dataset.json', 'w') as f:
                json.dump(training_data, f, indent=2, default=str)
            print("AI training dataset saved to 'ai_training_dataset.json'")
            print(f"Total patterns: {len(training_data['patterns'])}")
            print(f"Successful patterns: {len(training_data['successful_patterns'])}")
        
        elif choice == "6":
            break
        
        else:
            print("Invalid choice. Please try again.")
