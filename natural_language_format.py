"""
Natural Language Pattern Analysis Format
Structured format for capturing your personal market observations
in natural language with precise time/price references for AI training
"""

from datetime import datetime
from typing import Dict, List
import json
import pandas as pd

class NaturalLanguageFormatter:
    def __init__(self):
        self.observation_template = self.create_template()
    
    def create_template(self) -> Dict:
        """Create standardized template for natural language observations"""
        return {
            # Metadata
            'observation_id': '',
            'analyst_name': 'Your Name',
            'created_at': '',
            'index': '',
            
            # Natural Language Analysis (Your Words)
            'natural_analysis': {
                'pattern_description': '',  # "I observed an ellipse pennant formation..."
                'time_narrative': '',       # "Starting from 2025-08-08 10:30, the price began..."
                'price_narrative': '',      # "At 24350 level, I noticed strong resistance..."
                'formation_story': '',      # "The pattern developed over 3 hours with..."
                'breakout_analysis': '',    # "The breakout occurred at 24400 with..."
                'prediction_reasoning': ''  # "Based on this pattern, I expect..."
            },
            
            # Precise References (For AI Processing)
            'precise_references': {
                'key_timestamps': [],       # Exact times you mention
                'key_price_levels': [],     # Exact prices you mention
                'pattern_coordinates': {},  # Start/end points of pattern
                'volume_observations': [],  # Volume-related observations
                'confluence_factors': []    # Multiple factors aligning
            },
            
            # Pattern Structure (Your Interpretation)
            'pattern_structure': {
                'pattern_name': '',         # Your name for the pattern
                'pattern_type': '',         # Category (continuation, reversal, etc.)
                'formation_time': '',       # How long it took to form
                'key_characteristics': [],  # What makes this pattern unique
                'similar_patterns': []      # References to similar past patterns
            },
            
            # Market Context (Your Perspective)
            'market_context': {
                'overall_market_view': '',  # Your view of broader market
                'sector_influence': '',     # Sector-specific factors
                'global_factors': '',       # Global market influence
                'news_events': '',          # Relevant news/events
                'institutional_activity': '' # Your view on institutional flow
            },
            
            # Prediction Framework (Your Method)
            'prediction_framework': {
                'expected_scenario': '',    # Most likely outcome
                'alternative_scenarios': [],# Other possible outcomes
                'target_methodology': '',   # How you calculated targets
                'risk_assessment': '',      # Your risk analysis
                'time_expectations': '',    # When you expect move to happen
                'confidence_reasoning': ''  # Why you have this confidence level
            }
        }
    
    def create_ellipse_pennant_example(self) -> Dict:
        """Example of how to document an ellipse pennant pattern"""
        example = self.create_template()
        
        example.update({
            'observation_id': 'EP_NIFTY_20250808_001',
            'analyst_name': 'Your Trading Name',
            'created_at': '2025-08-08T15:30:00',
            'index': 'Nifty',
            
            'natural_analysis': {
                'pattern_description': 'I observed a classic ellipse pennant formation on Nifty 5-minute chart. The pattern shows converging trend lines forming an elliptical shape with decreasing volatility.',
                
                'time_narrative': 'The pattern started forming at 2025-08-08 10:15 when price hit 24380. Over the next 4 hours until 14:15, price oscillated between converging support and resistance lines, creating the ellipse shape.',
                
                'price_narrative': 'Initial resistance at 24380 was tested 3 times. Support started at 24320 and gradually moved up to 24340. The apex of the ellipse formed around 24360 level at 14:00.',
                
                'formation_story': 'The ellipse developed as bulls and bears fought for control. Each swing high was lower than previous, and each swing low was higher, creating the characteristic narrowing pattern. Volume decreased as pattern matured.',
                
                'breakout_analysis': 'Breakout occurred at 14:20 above 24365 with increased volume. The breakout candle closed at 24375, confirming the upward resolution of the pattern.',
                
                'prediction_reasoning': 'Based on ellipse pennant theory and measuring the initial move, I expect target of 24420-24450. The pattern typically resolves in direction of prior trend, which was bullish.'
            },
            
            'precise_references': {
                'key_timestamps': [
                    {'time': '2025-08-08 10:15', 'significance': 'Pattern formation start', 'price': 24380},
                    {'time': '2025-08-08 12:30', 'significance': 'Mid-pattern consolidation', 'price': 24350},
                    {'time': '2025-08-08 14:00', 'significance': 'Pattern apex/narrowest point', 'price': 24360},
                    {'time': '2025-08-08 14:20', 'significance': 'Breakout confirmation', 'price': 24375}
                ],
                
                'key_price_levels': [
                    {'price': 24380, 'role': 'Initial resistance/pattern top'},
                    {'price': 24320, 'role': 'Initial support/pattern bottom'},
                    {'price': 24360, 'role': 'Pattern apex/breakout level'},
                    {'price': 24420, 'role': 'Primary target'},
                    {'price': 24450, 'role': 'Extended target'}
                ],
                
                'pattern_coordinates': {
                    'start_time': '2025-08-08 10:15',
                    'end_time': '2025-08-08 14:20',
                    'duration_minutes': 245,
                    'pattern_height': 60,  # 24380 - 24320
                    'breakout_point': 24365
                },
                
                'volume_observations': [
                    {'time': '2025-08-08 10:15', 'observation': 'High volume on initial move'},
                    {'time': '2025-08-08 12:00-14:00', 'observation': 'Decreasing volume during consolidation'},
                    {'time': '2025-08-08 14:20', 'observation': 'Volume spike on breakout confirmation'}
                ],
                
                'confluence_factors': [
                    'Pattern breakout coincided with global market strength',
                    'RSI was in neutral zone, allowing for upward move',
                    'Previous day\'s high at 24400 acting as magnetic level'
                ]
            },
            
            'pattern_structure': {
                'pattern_name': 'Ellipse Pennant',
                'pattern_type': 'Continuation',
                'formation_time': '4 hours 5 minutes',
                'key_characteristics': [
                    'Converging trend lines forming elliptical shape',
                    'Decreasing volume during formation',
                    'At least 5 touches on each trend line',
                    'Breakout with volume confirmation'
                ],
                'similar_patterns': [
                    'Symmetrical triangle but with curved boundaries',
                    'Similar to pennant but longer formation time'
                ]
            },
            
            'market_context': {
                'overall_market_view': 'Market in consolidation phase after recent decline. Looking for direction.',
                'sector_influence': 'Banking stocks showing relative strength, supporting index',
                'global_factors': 'US markets closed higher overnight, providing positive sentiment',
                'news_events': 'No major news during pattern formation, pure technical setup',
                'institutional_activity': 'Moderate FII buying observed in morning session'
            },
            
            'prediction_framework': {
                'expected_scenario': 'Upward breakout targeting 24420-24450 within next 2-3 hours',
                'alternative_scenarios': [
                    'False breakout leading to retest of 24340 support',
                    'Sideways movement if breakout lacks volume'
                ],
                'target_methodology': 'Measured move: Pattern height (60 points) added to breakout point (24365) = 24425',
                'risk_assessment': 'Stop loss at 24340 (below pattern support). Risk-reward ratio 1:2.5',
                'time_expectations': 'Target should be reached within 2-3 hours if breakout is genuine',
                'confidence_reasoning': 'High confidence due to clean pattern formation, volume confirmation, and supportive market context'
            }
        })
        
        return example
    
    def extract_ai_features(self, observation: Dict) -> Dict:
        """Extract features for AI training from natural language observation"""
        features = {
            # Pattern identification features
            'pattern_name': observation['pattern_structure']['pattern_name'],
            'pattern_type': observation['pattern_structure']['pattern_type'],
            'formation_duration': observation['pattern_structure']['formation_time'],
            
            # Time-based features
            'key_times': [ref['time'] for ref in observation['precise_references']['key_timestamps']],
            'pattern_start': observation['precise_references']['pattern_coordinates']['start_time'],
            'pattern_end': observation['precise_references']['pattern_coordinates']['end_time'],
            
            # Price-based features
            'key_levels': [ref['price'] for ref in observation['precise_references']['key_price_levels']],
            'pattern_height': observation['precise_references']['pattern_coordinates']['pattern_height'],
            'breakout_level': observation['precise_references']['pattern_coordinates']['breakout_point'],
            
            # Context features
            'market_sentiment': observation['market_context']['overall_market_view'],
            'confluence_count': len(observation['precise_references']['confluence_factors']),
            
            # Prediction features
            'expected_direction': 'bullish' if 'up' in observation['prediction_framework']['expected_scenario'].lower() else 'bearish',
            'confidence_level': observation['prediction_framework']['confidence_reasoning'],
            
            # Natural language text for NLP processing
            'pattern_description_text': observation['natural_analysis']['pattern_description'],
            'formation_story_text': observation['natural_analysis']['formation_story'],
            'prediction_reasoning_text': observation['natural_analysis']['prediction_reasoning']
        }
        
        return features
    
    def create_live_integration_format(self, observation: Dict) -> Dict:
        """Format observation for live market integration"""
        live_format = {
            'pattern_id': observation['observation_id'],
            'index': observation['index'],
            'timestamp': observation['created_at'],
            
            # Real-time monitoring parameters
            'monitoring_levels': {
                'breakout_level': observation['precise_references']['pattern_coordinates']['breakout_point'],
                'support_levels': [ref['price'] for ref in observation['precise_references']['key_price_levels'] if 'support' in ref['role'].lower()],
                'resistance_levels': [ref['price'] for ref in observation['precise_references']['key_price_levels'] if 'resistance' in ref['role'].lower()],
                'target_levels': [ref['price'] for ref in observation['precise_references']['key_price_levels'] if 'target' in ref['role'].lower()]
            },
            
            # Alert conditions
            'alert_conditions': {
                'breakout_confirmation': f"Price closes above {observation['precise_references']['pattern_coordinates']['breakout_point']} with volume",
                'pattern_failure': f"Price closes below pattern support",
                'target_achievement': "Price reaches any target level"
            },
            
            # AI analysis parameters
            'ai_parameters': {
                'pattern_type': observation['pattern_structure']['pattern_name'],
                'expected_direction': observation['prediction_framework']['expected_scenario'],
                'confidence_score': observation['prediction_framework']['confidence_reasoning'],
                'time_horizon': observation['prediction_framework']['time_expectations']
            }
        }
        
        return live_format

# Usage example
if __name__ == "__main__":
    formatter = NaturalLanguageFormatter()
    
    # Create example ellipse pennant observation
    example_observation = formatter.create_ellipse_pennant_example()
    
    # Save example
    with open('example_ellipse_pennant.json', 'w') as f:
        json.dump(example_observation, f, indent=2)
    
    # Extract AI features
    ai_features = formatter.extract_ai_features(example_observation)
    
    # Create live integration format
    live_format = formatter.create_live_integration_format(example_observation)
    
    print("Example observation created and saved!")
    print(f"Pattern: {example_observation['pattern_structure']['pattern_name']}")
    print(f"AI Features extracted: {len(ai_features)} features")
    print(f"Live integration format ready for real-time monitoring")
    
    # Show the natural language analysis
    print("\nYour Natural Language Analysis:")
    print(example_observation['natural_analysis']['pattern_description'])
    print(f"\nKey Times: {len(example_observation['precise_references']['key_timestamps'])}")
    print(f"Key Prices: {len(example_observation['precise_references']['key_price_levels'])}")
