"""
Two Day Analysis Validator and AI Training Data Generator
Validates your 2-day analysis against actual OHLC data and converts to AI training format
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List, Tuple
import numpy as np

class TwoDayAnalysisValidator:
    def __init__(self, csv_path: str = "Nifty.csv"):
        self.csv_path = csv_path
        self.df = pd.read_csv(csv_path)
        self.df['datetime'] = pd.to_datetime(self.df['datetime'])
        
    def get_day_data(self, date_str: str) -> pd.DataFrame:
        """Get data for specific date"""
        target_date = pd.to_datetime(date_str).date()
        return self.df[self.df['datetime'].dt.date == target_date].copy()
    
    def get_previous_day_close(self, date_str: str) -> float:
        """Get previous trading day close"""
        target_date = pd.to_datetime(date_str).date()
        previous_data = self.df[self.df['datetime'].dt.date < target_date]
        if not previous_data.empty:
            return previous_data.iloc[-1]['close']
        return None
    
    def validate_gap_analysis(self, date_str: str, claimed_gap: float, prev_close: float) -> Dict:
        """Validate gap up/down analysis"""
        day_data = self.get_day_data(date_str)
        if day_data.empty:
            return {'status': 'No data', 'actual_gap': 0}
        
        actual_open = day_data.iloc[0]['open']
        actual_gap = actual_open - prev_close if prev_close else 0
        
        return {
            'status': 'VALIDATED' if abs(actual_gap - claimed_gap) <= 5 else 'NEEDS_REVIEW',
            'claimed_gap': claimed_gap,
            'actual_gap': actual_gap,
            'previous_close': prev_close,
            'actual_open': actual_open,
            'gap_accuracy': abs(actual_gap - claimed_gap)
        }
    
    def validate_time_based_move(self, date_str: str, start_time: str, end_time: str, 
                                expected_direction: str) -> Dict:
        """Validate time-based market moves"""
        day_data = self.get_day_data(date_str)
        
        start_dt = pd.to_datetime(f"{date_str} {start_time}")
        end_dt = pd.to_datetime(f"{date_str} {end_time}")
        
        # Find closest candles
        start_candle = day_data.iloc[(day_data['datetime'] - start_dt).abs().argsort()[:1]]
        end_candle = day_data.iloc[(day_data['datetime'] - end_dt).abs().argsort()[:1]]
        
        if start_candle.empty or end_candle.empty:
            return {'status': 'No data found'}
        
        start_price = start_candle.iloc[0]['close']
        end_price = end_candle.iloc[0]['close']
        move_points = end_price - start_price
        
        actual_direction = 'bullish' if move_points > 0 else 'bearish' if move_points < 0 else 'sideways'
        
        return {
            'status': 'VALIDATED' if actual_direction == expected_direction.lower() else 'CONTRADICTED',
            'expected_direction': expected_direction,
            'actual_direction': actual_direction,
            'start_time': start_candle.iloc[0]['datetime'],
            'end_time': end_candle.iloc[0]['datetime'],
            'start_price': start_price,
            'end_price': end_price,
            'move_points': move_points,
            'move_percentage': (move_points / start_price) * 100
        }
    
    def validate_candlestick_pattern(self, date_str: str, time_str: str, pattern_name: str) -> Dict:
        """Validate candlestick pattern identification"""
        day_data = self.get_day_data(date_str)
        target_dt = pd.to_datetime(f"{date_str} {time_str}")
        
        # Find closest candle
        closest_candle = day_data.iloc[(day_data['datetime'] - target_dt).abs().argsort()[:1]]
        
        if closest_candle.empty:
            return {'status': 'No data found'}
        
        candle = closest_candle.iloc[0]
        
        # Calculate candle characteristics
        body_size = abs(candle['close'] - candle['open'])
        upper_shadow = candle['high'] - max(candle['open'], candle['close'])
        lower_shadow = min(candle['open'], candle['close']) - candle['low']
        total_range = candle['high'] - candle['low']
        
        # Pattern validation logic
        pattern_match = self.identify_pattern(body_size, upper_shadow, lower_shadow, total_range, candle)
        
        return {
            'status': 'VALIDATED' if pattern_name.lower() in pattern_match.lower() else 'NEEDS_REVIEW',
            'claimed_pattern': pattern_name,
            'identified_pattern': pattern_match,
            'candle_data': {
                'datetime': candle['datetime'],
                'open': candle['open'],
                'high': candle['high'],
                'low': candle['low'],
                'close': candle['close'],
                'body_size': body_size,
                'upper_shadow': upper_shadow,
                'lower_shadow': lower_shadow,
                'total_range': total_range
            }
        }
    
    def identify_pattern(self, body_size: float, upper_shadow: float, lower_shadow: float, 
                        total_range: float, candle: pd.Series) -> str:
        """Identify candlestick pattern based on proportions"""
        
        # Pattern identification logic
        if body_size < total_range * 0.3:  # Small body
            if upper_shadow > body_size * 2 and lower_shadow < body_size:
                return "Inverted Hammer" if candle['close'] > candle['open'] else "Shooting Star"
            elif lower_shadow > body_size * 2 and upper_shadow < body_size:
                return "Hammer" if candle['close'] > candle['open'] else "Hanging Man"
            elif upper_shadow > body_size and lower_shadow > body_size:
                return "Spinning Top"
        
        elif body_size > total_range * 0.7:  # Large body
            return "Marubozu"
        
        else:  # Medium body
            if candle['close'] > candle['open']:
                return "Bullish Candle"
            else:
                return "Bearish Candle"
        
        return "Standard Candle"
    
    def validate_support_resistance(self, date_str: str, level: float, role: str, 
                                  test_times: List[str]) -> Dict:
        """Validate support/resistance level behavior"""
        day_data = self.get_day_data(date_str)
        validations = []
        
        for time_str in test_times:
            target_dt = pd.to_datetime(f"{date_str} {time_str}")
            closest_candle = day_data.iloc[(day_data['datetime'] - target_dt).abs().argsort()[:1]]
            
            if not closest_candle.empty:
                candle = closest_candle.iloc[0]
                
                # Check if level acted as support/resistance
                if role.lower() == 'support':
                    level_test = candle['low'] <= level <= candle['high']
                    bounce = candle['close'] > level
                else:  # resistance
                    level_test = candle['low'] <= level <= candle['high']
                    bounce = candle['close'] < level
                
                validations.append({
                    'time': candle['datetime'],
                    'level_tested': level_test,
                    'appropriate_reaction': bounce,
                    'candle_data': {
                        'open': candle['open'],
                        'high': candle['high'],
                        'low': candle['low'],
                        'close': candle['close']
                    }
                })
        
        successful_tests = sum(1 for v in validations if v['level_tested'] and v['appropriate_reaction'])
        
        return {
            'status': 'VALIDATED' if successful_tests >= len(test_times) * 0.6 else 'PARTIAL',
            'level': level,
            'role': role,
            'total_tests': len(test_times),
            'successful_tests': successful_tests,
            'success_rate': (successful_tests / len(test_times)) * 100 if test_times else 0,
            'test_details': validations
        }
    
    def validate_jan_1_2018(self) -> Dict:
        """Validate January 1, 2018 analysis"""
        
        jan_1_analysis = {
            'date': '2018-01-01',
            'your_analysis': """So market opens around 10 points gapup from yesterdays close 10467.40 and remained sideways to bearish till the close of 1:20. and from 1:25 it give one bullish move till 2:20 close and from 2:25 candle it started bearish move and with one green candle which is somewhat looking inverted hammer at 2:40 the bearish move intesifies with the inverted hanging man at 2:45 and the 3:05 candle forms the hanging man at the bottom
Throughout the day inverted hammer was taking market upside and at 11:35 that fails and the bearish trend continues
In other terms we can say that market was in the parallel channel from 9:25 to 2:45 and the 2:50 candle breaks the parallel channel with big IFC""",
            'validations': []
        }
        
        # 1. Gap up validation
        gap_validation = self.validate_gap_analysis('2018-01-01', 10, 10467.40)
        jan_1_analysis['validations'].append({
            'claim': 'Market opens around 10 points gap up from yesterday close 10467.40',
            'validation': gap_validation
        })
        
        # 2. Sideways to bearish till 1:20
        sideways_validation = self.validate_time_based_move('2018-01-01', '09:20', '13:20', 'bearish')
        jan_1_analysis['validations'].append({
            'claim': 'Remained sideways to bearish till 1:20',
            'validation': sideways_validation
        })
        
        # 3. Bullish move 1:25 to 2:20
        bullish_validation = self.validate_time_based_move('2018-01-01', '13:25', '14:20', 'bullish')
        jan_1_analysis['validations'].append({
            'claim': 'Bullish move from 1:25 to 2:20',
            'validation': bullish_validation
        })
        
        # 4. Bearish from 2:25
        bearish_validation = self.validate_time_based_move('2018-01-01', '14:25', '15:25', 'bearish')
        jan_1_analysis['validations'].append({
            'claim': 'Bearish move from 2:25',
            'validation': bearish_validation
        })
        
        # 5. Inverted hammer at 2:40
        hammer_validation = self.validate_candlestick_pattern('2018-01-01', '14:40', 'Inverted Hammer')
        jan_1_analysis['validations'].append({
            'claim': 'Inverted hammer at 2:40',
            'validation': hammer_validation
        })
        
        # 6. Hanging man at 2:45
        hanging_validation = self.validate_candlestick_pattern('2018-01-01', '14:45', 'Hanging Man')
        jan_1_analysis['validations'].append({
            'claim': 'Hanging man at 2:45',
            'validation': hanging_validation
        })
        
        # 7. Hanging man at 3:05
        bottom_validation = self.validate_candlestick_pattern('2018-01-01', '15:05', 'Hanging Man')
        jan_1_analysis['validations'].append({
            'claim': 'Hanging man at 3:05 (bottom)',
            'validation': bottom_validation
        })
        
        return jan_1_analysis
    
    def validate_jan_2_2018(self) -> Dict:
        """Validate January 2, 2018 analysis"""
        
        jan_2_analysis = {
            'date': '2018-01-02',
            'your_analysis': """So market formed the w previous day from the 3:00 to 3:25 then opened gapup around 45 points with inverted hanging man with long upper shadow and then market continues the bearish stance till 10:05 candle and with that candle and its next candle market forms the bullish engulfing and goes upside till 10:45. the 10:40 candle is similar to the 9:15 candle just somewhat small which shows the bearish sign the 9:40 candle to 10:25 candle market forms the small inverted head n shoulder then the retesting/ liquidity absrption took place till 11:20 candle and then went upside. The closing point of the 10:05 acted as the support and market took one tap support from that level at 2:15 candle. In terms of the supply, which came form 9:15 candle till 10:05 candle is the double supply of the 1:50 to the 2:15 candle and then at 2:35 market forms the proper Green Hammer and went upside till 3:00 same demand which formed from 10:10 candle to 10:45 candle and forms red spinning top candle at 3:05 showing indecisiveness, but in spinning top cases if the next 2 to 3 candles breaks the high or low of the spinning top with body close market goes in that direction and at the end market forms inverted hammer at 3:25. the wick of the candle formed on 1 jan 2018 3:20 acted as an the support Resistance interchange (SR interchange). It worked as the support on 9:50 candle and then breaks that on 10:00 and then again act as resistance on 10:15, breaks that on 10:20 again act as an support on 11:25 as well as 1:00 and then breaks that support on the 1:55 and then 2:05 candle breaks the resistance but leaves only the shadow didn't closes the body above SR interchange and the breaks the resistance on 2:45 and the next candle to it act as and support to the SR""",
            'validations': []
        }
        
        # Get Jan 1 close for gap calculation
        jan_1_data = self.get_day_data('2018-01-01')
        jan_1_close = jan_1_data.iloc[-1]['close'] if not jan_1_data.empty else None
        
        # 1. Gap up validation
        gap_validation = self.validate_gap_analysis('2018-01-02', 45, jan_1_close)
        jan_2_analysis['validations'].append({
            'claim': 'Opened gap up around 45 points',
            'validation': gap_validation
        })
        
        # 2. Opening candle pattern
        opening_pattern = self.validate_candlestick_pattern('2018-01-02', '09:15', 'Inverted Hanging Man')
        jan_2_analysis['validations'].append({
            'claim': 'Opened with inverted hanging man with long upper shadow',
            'validation': opening_pattern
        })
        
        # 3. Bearish till 10:05
        bearish_move = self.validate_time_based_move('2018-01-02', '09:15', '10:05', 'bearish')
        jan_2_analysis['validations'].append({
            'claim': 'Bearish stance till 10:05',
            'validation': bearish_move
        })
        
        # 4. Bullish engulfing at 10:05-10:10
        engulfing_validation = self.validate_candlestick_pattern('2018-01-02', '10:10', 'Bullish Engulfing')
        jan_2_analysis['validations'].append({
            'claim': 'Bullish engulfing at 10:05-10:10',
            'validation': engulfing_validation
        })
        
        # 5. Upside move till 10:45
        upside_move = self.validate_time_based_move('2018-01-02', '10:10', '10:45', 'bullish')
        jan_2_analysis['validations'].append({
            'claim': 'Goes upside till 10:45',
            'validation': upside_move
        })
        
        # 6. Green Hammer at 2:35
        hammer_validation = self.validate_candlestick_pattern('2018-01-02', '14:35', 'Hammer')
        jan_2_analysis['validations'].append({
            'claim': 'Green Hammer at 2:35',
            'validation': hammer_validation
        })
        
        # 7. Spinning top at 3:05
        spinning_validation = self.validate_candlestick_pattern('2018-01-02', '15:05', 'Spinning Top')
        jan_2_analysis['validations'].append({
            'claim': 'Red spinning top at 3:05',
            'validation': spinning_validation
        })
        
        # 8. Inverted hammer at 3:25
        final_hammer = self.validate_candlestick_pattern('2018-01-02', '15:25', 'Inverted Hammer')
        jan_2_analysis['validations'].append({
            'claim': 'Inverted hammer at 3:25',
            'validation': final_hammer
        })
        
        # 9. SR interchange validation (using Jan 1 3:20 wick level)
        jan_1_320_data = self.get_day_data('2018-01-01')
        jan_1_320_candle = jan_1_320_data[jan_1_320_data['datetime'].dt.time == pd.to_datetime('15:20').time()]
        if not jan_1_320_candle.empty:
            sr_level = jan_1_320_candle.iloc[0]['high']  # Using high as wick level
            sr_validation = self.validate_support_resistance('2018-01-02', sr_level, 'support', 
                                                           ['09:50', '11:25', '13:00'])
            jan_2_analysis['validations'].append({
                'claim': 'SR interchange from Jan 1 3:20 wick',
                'validation': sr_validation
            })
        
        return jan_2_analysis
    
    def generate_ai_training_suggestions(self, validations: List[Dict]) -> Dict:
        """Generate suggestions for more robust AI training"""
        
        suggestions = {
            'accuracy_improvements': [],
            'specificity_enhancements': [],
            'pattern_refinements': [],
            'additional_data_points': [],
            'measurement_precision': []
        }
        
        # Analyze validation results
        validated_count = sum(1 for v in validations if any(
            val.get('validation', {}).get('status') == 'VALIDATED' 
            for val in v.get('validations', [])
        ))
        
        total_claims = sum(len(v.get('validations', [])) for v in validations)
        accuracy_rate = (validated_count / total_claims) * 100 if total_claims > 0 else 0
        
        # Accuracy improvements
        if accuracy_rate < 70:
            suggestions['accuracy_improvements'].extend([
                "Add exact price levels for key turning points",
                "Include volume confirmation for pattern validity",
                "Specify exact candle close levels for trend changes",
                "Add percentage moves for better quantification"
            ])
        
        # Specificity enhancements
        suggestions['specificity_enhancements'].extend([
            "Add exact entry/exit prices for each pattern",
            "Include stop-loss levels for risk management",
            "Specify target levels with reasoning",
            "Add confidence levels (High/Medium/Low) for each observation",
            "Include market context (trending/ranging/volatile)",
            "Add session-based analysis (opening/mid-day/closing)",
            "Specify pattern completion criteria"
        ])
        
        # Pattern refinements
        suggestions['pattern_refinements'].extend([
            "Define exact shadow-to-body ratios for candlestick patterns",
            "Add pattern failure conditions",
            "Include pattern confirmation requirements",
            "Specify minimum/maximum pattern duration",
            "Add pattern strength indicators",
            "Include false signal identification"
        ])
        
        # Additional data points
        suggestions['additional_data_points'].extend([
            "Previous day's high/low levels",
            "Opening gap percentage",
            "Intraday range as percentage of previous day",
            "Volume profile analysis",
            "Market breadth indicators",
            "Sector performance correlation",
            "Global market influence",
            "News/event impact assessment"
        ])
        
        # Measurement precision
        suggestions['measurement_precision'].extend([
            "Use exact timestamps (HH:MM:SS)",
            "Include tick-level precision for key levels",
            "Add percentage calculations for all moves",
            "Include risk-reward ratios",
            "Add time-based performance metrics",
            "Include pattern success probability",
            "Add market efficiency measurements"
        ])
        
        return suggestions

def main():
    """Main validation and training data generation"""
    
    print("🔍 VALIDATING YOUR 2-DAY ANALYSIS")
    print("=" * 50)
    
    validator = TwoDayAnalysisValidator()
    
    # Validate both days
    jan_1_results = validator.validate_jan_1_2018()
    jan_2_results = validator.validate_jan_2_2018()
    
    # Print validation results
    for day_results in [jan_1_results, jan_2_results]:
        print(f"\n📅 {day_results['date']} VALIDATION RESULTS:")
        print("-" * 40)
        
        validated_count = 0
        total_count = len(day_results['validations'])
        
        for i, validation in enumerate(day_results['validations'], 1):
            status = validation['validation'].get('status', 'UNKNOWN')
            status_emoji = "✅" if status == 'VALIDATED' else "⚠️" if status == 'PARTIAL' else "❌" if status == 'CONTRADICTED' else "❓"
            
            print(f"{i}. {status_emoji} {validation['claim']}")
            print(f"   Status: {status}")
            
            if status == 'VALIDATED':
                validated_count += 1
            
            # Show key details
            val_data = validation['validation']
            if 'actual_gap' in val_data:
                print(f"   Claimed: {val_data['claimed_gap']} points, Actual: {val_data['actual_gap']:.2f} points")
            elif 'move_points' in val_data:
                print(f"   Move: {val_data['move_points']:.2f} points ({val_data['actual_direction']})")
            elif 'identified_pattern' in val_data:
                print(f"   Pattern: {val_data['identified_pattern']}")
            
            print()
        
        accuracy = (validated_count / total_count) * 100 if total_count > 0 else 0
        print(f"📊 Accuracy: {validated_count}/{total_count} ({accuracy:.1f}%)")
    
    # Generate AI training suggestions
    suggestions = validator.generate_ai_training_suggestions([jan_1_results, jan_2_results])
    
    print(f"\n🤖 AI TRAINING ENHANCEMENT SUGGESTIONS:")
    print("=" * 50)
    
    for category, items in suggestions.items():
        if items:
            print(f"\n{category.replace('_', ' ').title()}:")
            for item in items[:5]:  # Show top 5 suggestions per category
                print(f"• {item}")
    
    # Save results
    results = {
        'jan_1_2018': jan_1_results,
        'jan_2_2018': jan_2_results,
        'ai_training_suggestions': suggestions,
        'generated_at': datetime.now().isoformat()
    }
    
    with open('two_day_validation_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to 'two_day_validation_results.json'")
    
    return results

if __name__ == "__main__":
    results = main()
