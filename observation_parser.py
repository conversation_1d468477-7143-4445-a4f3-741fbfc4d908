"""
Observation Parser
Converts your daily written observations into structured AI training data
"""

import os
import re
import json
from datetime import datetime
from typing import Dict, List, Tuple
import pandas as pd

class ObservationParser:
    def __init__(self, data_path: str = "."):
        self.data_path = data_path
        self.indices_data = self.load_market_data()
        
    def load_market_data(self):
        """Load OHLC data for price validation"""
        indices_data = {}
        indices = ['Nifty', 'Banknifty', 'Finnifty', 'Midcap', 'Sensex']
        
        for index in indices:
            try:
                df = pd.read_csv(f"{self.data_path}/{index}.csv")
                df['datetime'] = pd.to_datetime(df['datetime'])
                indices_data[index] = df
            except Exception as e:
                print(f"Could not load {index}: {e}")
        
        return indices_data
    
    def extract_time_references(self, text: str) -> List[Dict]:
        """Extract time references from text"""
        time_patterns = [
            r'(\d{1,2}:\d{2})\s*(?:AM|PM|am|pm)?',  # 10:30 AM
            r'at\s+(\d{1,2}:\d{2})',                # at 10:30
            r'(\d{1,2}\.\d{2})\s*(?:AM|PM|am|pm)?', # 10.30 AM
        ]
        
        time_refs = []
        for pattern in time_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                time_str = match.group(1)
                context_start = max(0, match.start() - 50)
                context_end = min(len(text), match.end() + 50)
                context = text[context_start:context_end].strip()
                
                time_refs.append({
                    'time': time_str,
                    'context': context,
                    'position': match.start()
                })
        
        return time_refs
    
    def extract_price_references(self, text: str) -> List[Dict]:
        """Extract price level references from text"""
        # Price patterns for Indian indices (typically 20000-30000 range for Nifty)
        price_patterns = [
            r'(\d{4,5}(?:\.\d{1,2})?)',  # 24350, 24350.50
            r'at\s+(\d{4,5})',           # at 24350
            r'level\s+(\d{4,5})',        # level 24350
            r'(\d{4,5})\s+level',        # 24350 level
        ]
        
        price_refs = []
        for pattern in price_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                price_str = match.group(1)
                try:
                    price_val = float(price_str)
                    # Filter for reasonable index price ranges
                    if 15000 <= price_val <= 85000:  # Covers all major indices
                        context_start = max(0, match.start() - 50)
                        context_end = min(len(text), match.end() + 50)
                        context = text[context_start:context_end].strip()
                        
                        price_refs.append({
                            'price': price_val,
                            'context': context,
                            'position': match.start()
                        })
                except ValueError:
                    continue
        
        return price_refs
    
    def extract_pattern_references(self, text: str) -> List[Dict]:
        """Extract pattern mentions from text"""
        pattern_keywords = [
            'ellipse pennant', 'double bottom', 'double top', 'head and shoulders',
            'triangle', 'ascending triangle', 'descending triangle', 'symmetrical triangle',
            'flag', 'pennant', 'wedge', 'channel', 'breakout', 'breakdown',
            'support', 'resistance', 'bounce', 'rejection', 'reversal',
            'continuation', 'consolidation', 'range', 'trend line'
        ]
        
        pattern_refs = []
        text_lower = text.lower()
        
        for pattern in pattern_keywords:
            if pattern in text_lower:
                # Find all occurrences
                start = 0
                while True:
                    pos = text_lower.find(pattern, start)
                    if pos == -1:
                        break
                    
                    context_start = max(0, pos - 100)
                    context_end = min(len(text), pos + len(pattern) + 100)
                    context = text[context_start:context_end].strip()
                    
                    pattern_refs.append({
                        'pattern': pattern,
                        'context': context,
                        'position': pos
                    })
                    
                    start = pos + len(pattern)
        
        return pattern_refs
    
    def extract_predictions(self, text: str) -> List[Dict]:
        """Extract prediction statements from text"""
        prediction_indicators = [
            r'expect(?:ing)?\s+([^.]+)',
            r'predict(?:ion)?\s+([^.]+)',
            r'target(?:ing)?\s+([^.]+)',
            r'should\s+([^.]+)',
            r'will\s+([^.]+)',
            r'likely\s+([^.]+)'
        ]
        
        predictions = []
        for pattern in prediction_indicators:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                prediction_text = match.group(1).strip()
                if len(prediction_text) > 5:  # Filter out very short matches
                    predictions.append({
                        'prediction': prediction_text,
                        'full_context': match.group(0),
                        'position': match.start()
                    })
        
        return predictions
    
    def extract_sentiment(self, text: str) -> Dict:
        """Extract sentiment indicators from text"""
        bullish_words = [
            'bullish', 'positive', 'strong', 'bounce', 'breakout', 'rally',
            'upward', 'higher', 'support', 'buying', 'strength', 'momentum'
        ]
        
        bearish_words = [
            'bearish', 'negative', 'weak', 'breakdown', 'fall', 'decline',
            'downward', 'lower', 'resistance', 'selling', 'weakness', 'pressure'
        ]
        
        neutral_words = [
            'sideways', 'consolidation', 'range', 'neutral', 'mixed', 'uncertain'
        ]
        
        text_lower = text.lower()
        
        bullish_count = sum(1 for word in bullish_words if word in text_lower)
        bearish_count = sum(1 for word in bearish_words if word in text_lower)
        neutral_count = sum(1 for word in neutral_words if word in text_lower)
        
        total_sentiment_words = bullish_count + bearish_count + neutral_count
        
        if total_sentiment_words == 0:
            return {'sentiment': 'neutral', 'confidence': 0}
        
        bullish_ratio = bullish_count / total_sentiment_words
        bearish_ratio = bearish_count / total_sentiment_words
        
        if bullish_ratio > bearish_ratio:
            sentiment = 'bullish'
            confidence = bullish_ratio
        elif bearish_ratio > bullish_ratio:
            sentiment = 'bearish'
            confidence = bearish_ratio
        else:
            sentiment = 'neutral'
            confidence = neutral_count / total_sentiment_words
        
        return {
            'sentiment': sentiment,
            'confidence': confidence,
            'bullish_words': bullish_count,
            'bearish_words': bearish_count,
            'neutral_words': neutral_count
        }
    
    def parse_daily_observation(self, file_path: str) -> Dict:
        """Parse a daily observation file into structured data"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract filename date
        filename = os.path.basename(file_path)
        date_match = re.search(r'(\d{4}-\d{2}-\d{2})', filename)
        observation_date = date_match.group(1) if date_match else 'unknown'
        
        # Extract all components
        time_refs = self.extract_time_references(content)
        price_refs = self.extract_price_references(content)
        pattern_refs = self.extract_pattern_references(content)
        predictions = self.extract_predictions(content)
        sentiment = self.extract_sentiment(content)
        
        # Structure the parsed data
        parsed_data = {
            'metadata': {
                'file_path': file_path,
                'observation_date': observation_date,
                'parsed_at': datetime.now().isoformat(),
                'content_length': len(content)
            },
            
            'raw_content': content,
            
            'extracted_features': {
                'time_references': time_refs,
                'price_references': price_refs,
                'pattern_references': pattern_refs,
                'predictions': predictions,
                'sentiment_analysis': sentiment
            },
            
            'ai_training_features': {
                'total_time_refs': len(time_refs),
                'total_price_refs': len(price_refs),
                'total_patterns': len(pattern_refs),
                'total_predictions': len(predictions),
                'dominant_sentiment': sentiment['sentiment'],
                'sentiment_confidence': sentiment['confidence'],
                'unique_patterns': list(set([p['pattern'] for p in pattern_refs])),
                'price_range': {
                    'min_price': min([p['price'] for p in price_refs]) if price_refs else None,
                    'max_price': max([p['price'] for p in price_refs]) if price_refs else None
                }
            }
        }
        
        return parsed_data
    
    def batch_parse_observations(self, directory: str = "daily_notes") -> List[Dict]:
        """Parse all observation files in a directory"""
        if not os.path.exists(directory):
            print(f"Directory {directory} not found!")
            return []
        
        parsed_observations = []
        
        for filename in os.listdir(directory):
            if filename.endswith('.txt'):
                file_path = os.path.join(directory, filename)
                try:
                    parsed_data = self.parse_daily_observation(file_path)
                    parsed_observations.append(parsed_data)
                    print(f"✅ Parsed: {filename}")
                except Exception as e:
                    print(f"❌ Error parsing {filename}: {e}")
        
        return parsed_observations
    
    def generate_ai_training_dataset(self, parsed_observations: List[Dict]) -> Dict:
        """Generate comprehensive AI training dataset"""
        training_dataset = {
            'metadata': {
                'total_observations': len(parsed_observations),
                'generated_at': datetime.now().isoformat(),
                'date_range': self.get_date_range(parsed_observations)
            },
            
            'pattern_library': self.build_pattern_library(parsed_observations),
            'sentiment_patterns': self.analyze_sentiment_patterns(parsed_observations),
            'time_price_correlations': self.analyze_time_price_correlations(parsed_observations),
            'prediction_accuracy': self.analyze_prediction_patterns(parsed_observations),
            
            'training_examples': []
        }
        
        # Create training examples
        for obs in parsed_observations:
            training_example = {
                'date': obs['metadata']['observation_date'],
                'input_features': {
                    'patterns_mentioned': obs['ai_training_features']['unique_patterns'],
                    'sentiment': obs['ai_training_features']['dominant_sentiment'],
                    'price_levels': [p['price'] for p in obs['extracted_features']['price_references']],
                    'time_points': [t['time'] for t in obs['extracted_features']['time_references']]
                },
                'natural_language_analysis': obs['raw_content'],
                'structured_predictions': [p['prediction'] for p in obs['extracted_features']['predictions']]
            }
            training_dataset['training_examples'].append(training_example)
        
        return training_dataset
    
    def get_date_range(self, observations: List[Dict]) -> Dict:
        """Get date range of observations"""
        dates = [obs['metadata']['observation_date'] for obs in observations if obs['metadata']['observation_date'] != 'unknown']
        if not dates:
            return {'start': None, 'end': None}
        
        return {
            'start': min(dates),
            'end': max(dates),
            'total_days': len(set(dates))
        }
    
    def build_pattern_library(self, observations: List[Dict]) -> Dict:
        """Build pattern library from observations"""
        pattern_counts = {}
        pattern_contexts = {}
        
        for obs in observations:
            for pattern_ref in obs['extracted_features']['pattern_references']:
                pattern = pattern_ref['pattern']
                pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
                
                if pattern not in pattern_contexts:
                    pattern_contexts[pattern] = []
                pattern_contexts[pattern].append(pattern_ref['context'])
        
        return {
            'pattern_frequency': pattern_counts,
            'pattern_contexts': pattern_contexts,
            'most_common_patterns': sorted(pattern_counts.items(), key=lambda x: x[1], reverse=True)[:10]
        }
    
    def analyze_sentiment_patterns(self, observations: List[Dict]) -> Dict:
        """Analyze sentiment patterns over time"""
        sentiment_timeline = []
        
        for obs in observations:
            sentiment_timeline.append({
                'date': obs['metadata']['observation_date'],
                'sentiment': obs['ai_training_features']['dominant_sentiment'],
                'confidence': obs['ai_training_features']['sentiment_confidence']
            })
        
        return {
            'sentiment_timeline': sentiment_timeline,
            'sentiment_distribution': self.calculate_sentiment_distribution(sentiment_timeline)
        }
    
    def calculate_sentiment_distribution(self, timeline: List[Dict]) -> Dict:
        """Calculate sentiment distribution"""
        sentiments = [item['sentiment'] for item in timeline]
        total = len(sentiments)
        
        return {
            'bullish': sentiments.count('bullish') / total if total > 0 else 0,
            'bearish': sentiments.count('bearish') / total if total > 0 else 0,
            'neutral': sentiments.count('neutral') / total if total > 0 else 0
        }
    
    def analyze_time_price_correlations(self, observations: List[Dict]) -> Dict:
        """Analyze correlations between time references and price movements"""
        time_price_pairs = []
        
        for obs in observations:
            times = obs['extracted_features']['time_references']
            prices = obs['extracted_features']['price_references']
            
            for time_ref in times:
                for price_ref in prices:
                    # Simple proximity check
                    if abs(time_ref['position'] - price_ref['position']) < 100:  # Within 100 characters
                        time_price_pairs.append({
                            'time': time_ref['time'],
                            'price': price_ref['price'],
                            'date': obs['metadata']['observation_date']
                        })
        
        return {
            'total_correlations': len(time_price_pairs),
            'time_price_pairs': time_price_pairs
        }
    
    def analyze_prediction_patterns(self, observations: List[Dict]) -> Dict:
        """Analyze prediction patterns"""
        all_predictions = []
        
        for obs in observations:
            for pred in obs['extracted_features']['predictions']:
                all_predictions.append({
                    'date': obs['metadata']['observation_date'],
                    'prediction': pred['prediction'],
                    'context': pred['full_context']
                })
        
        return {
            'total_predictions': len(all_predictions),
            'predictions_by_date': all_predictions
        }

# CLI Interface
if __name__ == "__main__":
    parser = ObservationParser()
    
    print("🔍 OBSERVATION PARSER")
    print("=" * 30)
    
    while True:
        print("\nOptions:")
        print("1. Parse single observation file")
        print("2. Parse all observations in daily_notes")
        print("3. Generate AI training dataset")
        print("4. Exit")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            file_path = input("Enter path to observation file: ").strip()
            if os.path.exists(file_path):
                parsed = parser.parse_daily_observation(file_path)
                print(f"\n✅ Parsed successfully!")
                print(f"📊 Found: {parsed['ai_training_features']['total_patterns']} patterns")
                print(f"💰 Found: {parsed['ai_training_features']['total_price_refs']} price references")
                print(f"⏰ Found: {parsed['ai_training_features']['total_time_refs']} time references")
                print(f"🎯 Found: {parsed['ai_training_features']['total_predictions']} predictions")
                print(f"😊 Sentiment: {parsed['ai_training_features']['dominant_sentiment']}")
            else:
                print("❌ File not found!")
        
        elif choice == "2":
            parsed_observations = parser.batch_parse_observations()
            print(f"\n✅ Parsed {len(parsed_observations)} observation files")
            
            if parsed_observations:
                # Save parsed data
                with open('parsed_observations.json', 'w') as f:
                    json.dump(parsed_observations, f, indent=2, default=str)
                print("💾 Saved to 'parsed_observations.json'")
        
        elif choice == "3":
            # Load existing parsed data or parse fresh
            if os.path.exists('parsed_observations.json'):
                with open('parsed_observations.json', 'r') as f:
                    parsed_observations = json.load(f)
            else:
                parsed_observations = parser.batch_parse_observations()
            
            if parsed_observations:
                training_dataset = parser.generate_ai_training_dataset(parsed_observations)
                
                with open('ai_training_dataset.json', 'w') as f:
                    json.dump(training_dataset, f, indent=2, default=str)
                
                print(f"\n🤖 AI Training Dataset Generated!")
                print(f"📊 Total observations: {training_dataset['metadata']['total_observations']}")
                print(f"📅 Date range: {training_dataset['metadata']['date_range']['start']} to {training_dataset['metadata']['date_range']['end']}")
                print(f"🎯 Most common patterns: {[p[0] for p in training_dataset['pattern_library']['most_common_patterns'][:3]]}")
                print("💾 Saved to 'ai_training_dataset.json'")
            else:
                print("❌ No observations found to process!")
        
        elif choice == "4":
            break
        
        else:
            print("❌ Invalid choice. Try again.")
