"""
Market Observation Recorder
A tool to systematically record and analyze your market observations
"""

import pandas as pd
import json
from datetime import datetime
from typing import Dict, List
import os

class ObservationRecorder:
    def __init__(self, data_path: str = "."):
        self.data_path = data_path
        self.observations_file = "market_observations.json"
        self.observations = self.load_observations()
        
    def load_observations(self) -> List[Dict]:
        """Load existing observations from file"""
        if os.path.exists(self.observations_file):
            with open(self.observations_file, 'r') as f:
                return json.load(f)
        return []
    
    def save_observations(self):
        """Save observations to file"""
        with open(self.observations_file, 'w') as f:
            json.dump(self.observations, f, indent=2, default=str)
    
    def get_price_data_at_time(self, index: str, target_datetime: str) -> Dict:
        """Get OHLC data for specific datetime"""
        try:
            df = pd.read_csv(f"{self.data_path}/{index}.csv")
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            target_time = pd.to_datetime(target_datetime)
            closest_idx = df['datetime'].sub(target_time).abs().idxmin()
            closest_data = df.iloc[closest_idx]
            
            return {
                'datetime': str(closest_data['datetime']),
                'open': closest_data['open'],
                'high': closest_data['high'],
                'low': closest_data['low'],
                'close': closest_data['close'],
                'actual_datetime_used': str(closest_data['datetime'])
            }
        except Exception as e:
            print(f"Error getting price data: {e}")
            return {}
    
    def create_new_observation(self, 
                             index: str,
                             analysis_datetime: str,
                             observation_text: str,
                             pattern_type: str = "",
                             confidence: str = "Medium",
                             prediction: str = "",
                             key_levels: Dict = None) -> Dict:
        """Create a new structured observation"""
        
        price_data = self.get_price_data_at_time(index, analysis_datetime)
        
        observation = {
            'id': len(self.observations) + 1,
            'created_at': datetime.now().isoformat(),
            'index': index,
            'analysis_datetime': analysis_datetime,
            'price_data': price_data,
            'observation': {
                'text': observation_text,
                'pattern_type': pattern_type,
                'confidence_level': confidence,
                'prediction': prediction
            },
            'technical_analysis': {
                'support_levels': key_levels.get('support', []) if key_levels else [],
                'resistance_levels': key_levels.get('resistance', []) if key_levels else [],
                'key_price_points': []
            },
            'market_context': {
                'overall_trend': "",
                'volume_analysis': "",
                'global_factors': "",
                'sector_performance': ""
            },
            'outcome_tracking': {
                'prediction_accuracy': None,
                'actual_outcome': "",
                'lessons_learned': "",
                'follow_up_date': ""
            }
        }
        
        return observation
    
    def add_observation(self, observation: Dict):
        """Add observation to the database"""
        self.observations.append(observation)
        self.save_observations()
        print(f"Observation #{observation['id']} added successfully!")
    
    def update_observation_outcome(self, observation_id: int, outcome_data: Dict):
        """Update the outcome of a previous observation"""
        for obs in self.observations:
            if obs['id'] == observation_id:
                obs['outcome_tracking'].update(outcome_data)
                self.save_observations()
                print(f"Observation #{observation_id} updated with outcome!")
                return
        print(f"Observation #{observation_id} not found!")
    
    def search_observations(self, 
                          index: str = None,
                          pattern_type: str = None,
                          date_range: tuple = None) -> List[Dict]:
        """Search observations by criteria"""
        results = self.observations.copy()
        
        if index:
            results = [obs for obs in results if obs['index'].lower() == index.lower()]
        
        if pattern_type:
            results = [obs for obs in results if pattern_type.lower() in obs['observation']['pattern_type'].lower()]
        
        if date_range:
            start_date, end_date = date_range
            results = [obs for obs in results 
                      if start_date <= obs['analysis_datetime'] <= end_date]
        
        return results
    
    def generate_analysis_report(self, index: str = None) -> Dict:
        """Generate analysis report from observations"""
        observations = self.search_observations(index=index) if index else self.observations
        
        if not observations:
            return {"error": "No observations found"}
        
        # Pattern frequency analysis
        patterns = {}
        accuracy_scores = []
        
        for obs in observations:
            pattern = obs['observation']['pattern_type']
            if pattern:
                patterns[pattern] = patterns.get(pattern, 0) + 1
            
            if obs['outcome_tracking']['prediction_accuracy']:
                try:
                    score = float(obs['outcome_tracking']['prediction_accuracy'].split('/')[0])
                    accuracy_scores.append(score)
                except:
                    pass
        
        report = {
            'total_observations': len(observations),
            'index_analyzed': index or "All indices",
            'date_range': {
                'earliest': min([obs['analysis_datetime'] for obs in observations]),
                'latest': max([obs['analysis_datetime'] for obs in observations])
            },
            'pattern_frequency': patterns,
            'average_accuracy': sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0,
            'most_common_pattern': max(patterns.items(), key=lambda x: x[1])[0] if patterns else None
        }
        
        return report
    
    def interactive_observation_entry(self):
        """Interactive CLI for entering observations"""
        print("\n=== Market Observation Entry ===")
        
        # Get basic info
        index = input("Index (Nifty/Banknifty/Finnifty/Midcap/Sensex): ").strip()
        datetime_str = input("Analysis DateTime (YYYY-MM-DD HH:MM or leave empty for now): ").strip()
        
        if not datetime_str:
            datetime_str = datetime.now().strftime("%Y-%m-%d %H:%M")
        
        # Get observation details
        print("\nDescribe your market observation:")
        observation_text = input("Observation: ").strip()
        
        pattern_type = input("Pattern Type (e.g., 'Double Bottom', 'Breakout', 'Reversal'): ").strip()
        confidence = input("Confidence Level (High/Medium/Low): ").strip() or "Medium"
        prediction = input("Your Prediction: ").strip()
        
        # Get key levels
        print("\nKey Levels (press Enter to skip):")
        support_input = input("Support Levels (comma-separated): ").strip()
        resistance_input = input("Resistance Levels (comma-separated): ").strip()
        
        key_levels = {}
        if support_input:
            key_levels['support'] = [float(x.strip()) for x in support_input.split(',')]
        if resistance_input:
            key_levels['resistance'] = [float(x.strip()) for x in resistance_input.split(',')]
        
        # Create and add observation
        observation = self.create_new_observation(
            index=index,
            analysis_datetime=datetime_str,
            observation_text=observation_text,
            pattern_type=pattern_type,
            confidence=confidence,
            prediction=prediction,
            key_levels=key_levels
        )
        
        self.add_observation(observation)
        
        # Show price data context
        if observation['price_data']:
            print(f"\nPrice Context at {observation['price_data']['actual_datetime_used']}:")
            print(f"Open: {observation['price_data']['open']}")
            print(f"High: {observation['price_data']['high']}")
            print(f"Low: {observation['price_data']['low']}")
            print(f"Close: {observation['price_data']['close']}")

# Example usage
if __name__ == "__main__":
    recorder = ObservationRecorder()
    
    # Interactive mode
    while True:
        print("\n=== Market Analysis Observation System ===")
        print("1. Add New Observation")
        print("2. View Recent Observations")
        print("3. Search Observations")
        print("4. Generate Analysis Report")
        print("5. Update Observation Outcome")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == "1":
            recorder.interactive_observation_entry()
        
        elif choice == "2":
            recent = recorder.observations[-5:] if recorder.observations else []
            print(f"\nLast {len(recent)} observations:")
            for obs in recent:
                print(f"#{obs['id']}: {obs['index']} - {obs['analysis_datetime']} - {obs['observation']['pattern_type']}")
        
        elif choice == "3":
            index = input("Index to search (or Enter for all): ").strip() or None
            pattern = input("Pattern type to search (or Enter for all): ").strip() or None
            results = recorder.search_observations(index=index, pattern_type=pattern)
            print(f"\nFound {len(results)} observations:")
            for obs in results[:10]:  # Show first 10
                print(f"#{obs['id']}: {obs['index']} - {obs['analysis_datetime']} - {obs['observation']['pattern_type']}")
        
        elif choice == "4":
            index = input("Index for report (or Enter for all): ").strip() or None
            report = recorder.generate_analysis_report(index=index)
            print(f"\nAnalysis Report:")
            print(json.dumps(report, indent=2))
        
        elif choice == "5":
            obs_id = int(input("Observation ID to update: "))
            outcome = input("Actual outcome: ").strip()
            accuracy = input("Accuracy score (e.g., '8/10'): ").strip()
            lessons = input("Lessons learned: ").strip()
            
            outcome_data = {
                'actual_outcome': outcome,
                'prediction_accuracy': accuracy,
                'lessons_learned': lessons
            }
            recorder.update_observation_outcome(obs_id, outcome_data)
        
        elif choice == "6":
            break
        
        else:
            print("Invalid choice. Please try again.")
