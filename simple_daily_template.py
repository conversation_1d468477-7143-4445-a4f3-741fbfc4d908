"""
Simple Daily Journal Template C<PERSON>
Creates a simple text file where you can write your daily observations
"""

import os
from datetime import datetime

def create_simple_daily_template(date_str: str = None):
    """Create a simple daily template file"""
    if not date_str:
        date_str = datetime.now().strftime('%Y-%m-%d')
    
    # Create daily_notes directory if it doesn't exist
    if not os.path.exists('daily_notes'):
        os.makedirs('daily_notes')
    
    filename = f"daily_notes/market_observation_{date_str}.txt"
    
    template = f"""DAILY MARKET OBSERVATION - {date_str}
{'='*50}

TIME: {datetime.now().strftime('%H:%M')}

OVERALL MARKET VIEW:
[Write your overall view of today's market in your own words]


PATTERNS I OBSERVED TODAY:
[Describe any patterns you noticed - be specific with times and prices]

Example format:
- At 10:30 AM, I noticed Nifty forming an ellipse pennant between 24300-24400
- The pattern started at 09:45 when price hit resistance at 24380
- Breakout occurred at 14:20 above 24365 level


SPECIFIC TIME & PRICE OBSERVATIONS:
[Record exact times and prices you found significant]

Example:
- 09:45 AM: Nifty hit 24380 resistance, rejected with volume
- 11:30 AM: Strong support at 24320, bounced immediately  
- 14:20 PM: Breakout above 24365 with increased volume


INDEX-SPECIFIC NOTES:

NIFTY:
[Your observations on Nifty movements, patterns, levels]


BANK NIFTY:
[Your observations on Bank Nifty]


OTHER INDICES (Fin Nifty, Midcap, Sensex):
[Any observations on other indices]


KEY LEVELS IDENTIFIED:
Support Levels: [List specific price levels]
Resistance Levels: [List specific price levels]
Breakout Levels: [List specific price levels]


PREDICTIONS FOR TOMORROW/NEXT SESSION:
[What do you expect to happen next? Be specific with levels and reasoning]


MARKET SENTIMENT:
[Your read on overall sentiment - bullish/bearish/neutral and why]


VOLUME OBSERVATIONS:
[Any volume-related observations if you have access to volume data]


GLOBAL FACTORS:
[Any global market influences you noticed]


WHAT WORKED TODAY:
[Any successful predictions or analysis from previous days]


WHAT DIDN'T WORK:
[Any failed predictions - learning opportunities]


PATTERNS TO WATCH:
[Any developing patterns to monitor in coming days]


NOTES FOR AI LEARNING:
[Specific insights you want to remember and teach to AI]


CONFIDENCE LEVEL: [High/Medium/Low]
[Rate your confidence in today's analysis]


ADDITIONAL NOTES:
[Any other observations, thoughts, or insights]


---
END OF DAILY OBSERVATION
Date: {date_str}
Time: {datetime.now().strftime('%H:%M:%S')}
"""
    
    # Save the template
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(template)
    
    print(f"✅ Daily observation template created!")
    print(f"📁 File: {filename}")
    print(f"📝 Open this file in any text editor to write your observations")
    
    return filename

def create_quick_note_template():
    """Create a quick note template for immediate observations"""
    timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M')
    filename = f"daily_notes/quick_note_{timestamp}.txt"
    
    # Create directory if needed
    if not os.path.exists('daily_notes'):
        os.makedirs('daily_notes')
    
    template = f"""QUICK MARKET NOTE - {datetime.now().strftime('%Y-%m-%d %H:%M')}
{'='*40}

PATTERN OBSERVED:
[Describe the pattern you just noticed]

TIME: {datetime.now().strftime('%H:%M')}
INDEX: [Nifty/Bank Nifty/etc.]

KEY LEVELS:
- Entry/Formation: 
- Support: 
- Resistance: 
- Target: 
- Stop Loss: 

OBSERVATION:
[Write your detailed observation in your own words]


PREDICTION:
[What do you expect to happen?]


CONFIDENCE: [High/Medium/Low]

---
Quick note saved at: {datetime.now().strftime('%H:%M:%S')}
"""
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(template)
    
    print(f"✅ Quick note template created!")
    print(f"📁 File: {filename}")
    
    return filename

def list_existing_notes():
    """List all existing observation files"""
    if not os.path.exists('daily_notes'):
        print("No daily_notes directory found. Create your first observation!")
        return []
    
    files = []
    for filename in os.listdir('daily_notes'):
        if filename.endswith('.txt'):
            filepath = os.path.join('daily_notes', filename)
            files.append(filepath)
    
    files.sort(reverse=True)  # Most recent first
    
    print(f"\n📋 Your observation files:")
    for i, filepath in enumerate(files[:10], 1):  # Show last 10
        filename = os.path.basename(filepath)
        print(f"{i}. {filename}")
    
    return files

def main():
    """Main interface for daily journal"""
    print("🎯 SIMPLE DAILY MARKET JOURNAL")
    print("=" * 40)
    
    while True:
        print("\nOptions:")
        print("1. Create today's daily observation template")
        print("2. Create observation for specific date")
        print("3. Create quick note (for immediate observations)")
        print("4. List existing observation files")
        print("5. Exit")
        
        choice = input("\nSelect option (1-5): ").strip()
        
        if choice == "1":
            create_simple_daily_template()
            
        elif choice == "2":
            date_input = input("Enter date (YYYY-MM-DD): ").strip()
            try:
                datetime.strptime(date_input, '%Y-%m-%d')  # Validate
                create_simple_daily_template(date_input)
            except ValueError:
                print("❌ Invalid date format. Use YYYY-MM-DD")
                
        elif choice == "3":
            create_quick_note_template()
            
        elif choice == "4":
            list_existing_notes()
            
        elif choice == "5":
            print("👋 Happy trading!")
            break
            
        else:
            print("❌ Invalid choice. Try again.")

if __name__ == "__main__":
    main()
