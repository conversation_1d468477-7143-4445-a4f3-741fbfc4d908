"""
Daily Market Journal
A simple system where you write your daily market observations
in natural language, and it gets structured for AI training
"""

import os
import json
from datetime import datetime, date
from typing import Dict, List
import pandas as pd

class DailyMarketJournal:
    def __init__(self, data_path: str = "."):
        self.data_path = data_path
        self.journal_dir = "daily_observations"
        self.create_journal_structure()
        self.indices_data = self.load_market_data()
        
    def create_journal_structure(self):
        """Create directory structure for daily observations"""
        if not os.path.exists(self.journal_dir):
            os.makedirs(self.journal_dir)
        
        # Create subdirectories by year/month for organization
        current_year = datetime.now().year
        current_month = datetime.now().month
        
        year_dir = os.path.join(self.journal_dir, str(current_year))
        month_dir = os.path.join(year_dir, f"{current_month:02d}")
        
        os.makedirs(year_dir, exist_ok=True)
        os.makedirs(month_dir, exist_ok=True)
        
    def load_market_data(self):
        """Load OHLC data for price context"""
        indices_data = {}
        indices = ['Nifty', 'Banknifty', 'Finnifty', 'Midcap', 'Sensex']
        
        for index in indices:
            try:
                df = pd.read_csv(f"{self.data_path}/{index}.csv")
                df['datetime'] = pd.to_datetime(df['datetime'])
                indices_data[index] = df
                print(f"Loaded {index} data for context")
            except Exception as e:
                print(f"Could not load {index}: {e}")
        
        return indices_data
    
    def get_daily_price_summary(self, target_date: str = None) -> Dict:
        """Get price summary for all indices for a specific date"""
        if not target_date:
            target_date = datetime.now().strftime('%Y-%m-%d')
        
        target_dt = pd.to_datetime(target_date)
        summary = {}
        
        for index, df in self.indices_data.items():
            # Get data for the target date
            daily_data = df[df['datetime'].dt.date == target_dt.date()]
            
            if not daily_data.empty:
                summary[index] = {
                    'open': daily_data.iloc[0]['open'],
                    'high': daily_data['high'].max(),
                    'low': daily_data['low'].min(),
                    'close': daily_data.iloc[-1]['close'],
                    'range': daily_data['high'].max() - daily_data['low'].min(),
                    'data_points': len(daily_data)
                }
            else:
                summary[index] = {'status': 'No data for this date'}
        
        return summary
    
    def create_daily_entry_template(self, entry_date: str = None) -> str:
        """Create a template for daily journal entry"""
        if not entry_date:
            entry_date = datetime.now().strftime('%Y-%m-%d')
        
        # Get price context for the day
        price_summary = self.get_daily_price_summary(entry_date)
        
        template = f"""# Daily Market Observation - {entry_date}

## Market Context (Auto-generated)
"""
        
        for index, data in price_summary.items():
            if 'status' not in data:
                template += f"""
### {index}:
- Open: {data['open']:.2f}
- High: {data['high']:.2f}  
- Low: {data['low']:.2f}
- Close: {data['close']:.2f}
- Range: {data['range']:.2f} points
"""
        
        template += """
## Your Daily Analysis (Write your observations below)

### Overall Market View:
[Write your overall view of the market today]

### Key Patterns Observed:
[Describe any patterns you noticed - ellipse pennants, breakouts, etc.]

### Specific Time & Price Observations:
[Example: "At 10:30 AM, Nifty tested 24350 support level and bounced strongly"]

### Index-Specific Notes:

#### Nifty:
[Your observations on Nifty]

#### Bank Nifty:
[Your observations on Bank Nifty]

#### Other Indices:
[Any observations on Fin Nifty, Midcap, Sensex]

### Patterns Identified:
[List any specific patterns with time and price details]
[Example: "Ellipse pennant on Nifty from 11:00-15:00 between 24300-24400"]

### Key Levels for Tomorrow:
[Support and resistance levels you're watching]

### Predictions & Expectations:
[What you expect to happen next]

### Market Sentiment:
[Your read on overall market sentiment]

### Notes for AI Learning:
[Any specific insights you want the AI to learn from today's action]

---
## End of Daily Entry
"""
        
        return template
    
    def save_daily_entry(self, entry_date: str, content: str):
        """Save daily entry to file"""
        year = entry_date[:4]
        month = entry_date[5:7]
        
        file_path = os.path.join(self.journal_dir, year, month, f"{entry_date}.md")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Daily entry saved: {file_path}")
        return file_path
    
    def create_new_daily_entry(self, entry_date: str = None):
        """Create a new daily entry file"""
        if not entry_date:
            entry_date = datetime.now().strftime('%Y-%m-%d')
        
        # Check if entry already exists
        year = entry_date[:4]
        month = entry_date[5:7]
        file_path = os.path.join(self.journal_dir, year, month, f"{entry_date}.md")
        
        if os.path.exists(file_path):
            print(f"Entry for {entry_date} already exists at: {file_path}")
            return file_path
        
        # Create template
        template = self.create_daily_entry_template(entry_date)
        
        # Save template
        saved_path = self.save_daily_entry(entry_date, template)
        
        print(f"\n✅ Daily journal entry created for {entry_date}")
        print(f"📁 Location: {saved_path}")
        print(f"📝 Open this file in your text editor to write your observations")
        
        return saved_path
    
    def parse_daily_entry(self, file_path: str) -> Dict:
        """Parse a completed daily entry into structured data"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract date from filename
        filename = os.path.basename(file_path)
        entry_date = filename.replace('.md', '')
        
        # Simple parsing - in real implementation, you'd use more sophisticated NLP
        parsed_data = {
            'date': entry_date,
            'file_path': file_path,
            'raw_content': content,
            'structured_analysis': self.extract_structured_data(content),
            'ai_training_features': self.extract_ai_features(content)
        }
        
        return parsed_data
    
    def extract_structured_data(self, content: str) -> Dict:
        """Extract structured data from journal entry"""
        sections = {}
        current_section = None
        current_content = []
        
        lines = content.split('\n')
        
        for line in lines:
            if line.startswith('### ') or line.startswith('## '):
                # Save previous section
                if current_section:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # Start new section
                current_section = line.replace('#', '').replace(':', '').strip()
                current_content = []
            else:
                if current_section and line.strip():
                    current_content.append(line)
        
        # Save last section
        if current_section:
            sections[current_section] = '\n'.join(current_content).strip()
        
        return sections
    
    def extract_ai_features(self, content: str) -> Dict:
        """Extract features for AI training from journal content"""
        features = {
            'patterns_mentioned': [],
            'time_references': [],
            'price_references': [],
            'sentiment_indicators': [],
            'prediction_statements': []
        }
        
        # Simple keyword extraction (you can enhance this with NLP)
        content_lower = content.lower()
        
        # Pattern keywords
        pattern_keywords = ['ellipse pennant', 'double bottom', 'breakout', 'support', 'resistance', 'triangle', 'flag', 'pennant']
        for pattern in pattern_keywords:
            if pattern in content_lower:
                features['patterns_mentioned'].append(pattern)
        
        # Time references (simple regex would be better)
        import re
        time_matches = re.findall(r'\d{1,2}:\d{2}', content)
        features['time_references'] = time_matches
        
        # Price references
        price_matches = re.findall(r'\d{4,5}(?:\.\d{1,2})?', content)
        features['price_references'] = [float(p) for p in price_matches if 20000 <= float(p) <= 30000]  # Nifty range
        
        # Sentiment words
        bullish_words = ['bullish', 'positive', 'strong', 'bounce', 'breakout', 'rally']
        bearish_words = ['bearish', 'negative', 'weak', 'breakdown', 'fall', 'decline']
        
        for word in bullish_words:
            if word in content_lower:
                features['sentiment_indicators'].append(f'bullish_{word}')
        
        for word in bearish_words:
            if word in content_lower:
                features['sentiment_indicators'].append(f'bearish_{word}')
        
        return features
    
    def generate_weekly_summary(self, week_start_date: str = None) -> Dict:
        """Generate summary of the week's observations"""
        if not week_start_date:
            # Get current week start (Monday)
            today = datetime.now()
            days_since_monday = today.weekday()
            week_start = today - pd.Timedelta(days=days_since_monday)
            week_start_date = week_start.strftime('%Y-%m-%d')
        
        # Find all entries for the week
        week_entries = []
        for i in range(7):  # 7 days in a week
            check_date = pd.to_datetime(week_start_date) + pd.Timedelta(days=i)
            date_str = check_date.strftime('%Y-%m-%d')
            
            year = date_str[:4]
            month = date_str[5:7]
            file_path = os.path.join(self.journal_dir, year, month, f"{date_str}.md")
            
            if os.path.exists(file_path):
                parsed_entry = self.parse_daily_entry(file_path)
                week_entries.append(parsed_entry)
        
        # Generate summary
        summary = {
            'week_start': week_start_date,
            'entries_found': len(week_entries),
            'total_patterns_identified': 0,
            'most_mentioned_patterns': {},
            'key_insights': [],
            'ai_training_data': []
        }
        
        # Aggregate data
        all_patterns = []
        for entry in week_entries:
            patterns = entry['ai_training_features']['patterns_mentioned']
            all_patterns.extend(patterns)
            summary['ai_training_data'].append(entry['ai_training_features'])
        
        # Pattern frequency
        pattern_counts = {}
        for pattern in all_patterns:
            pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1
        
        summary['most_mentioned_patterns'] = pattern_counts
        summary['total_patterns_identified'] = len(all_patterns)
        
        return summary
    
    def interactive_daily_entry(self):
        """Interactive mode to create daily entry"""
        print("\n=== Daily Market Journal ===")
        
        # Ask for date
        date_input = input("Enter date (YYYY-MM-DD) or press Enter for today: ").strip()
        if not date_input:
            date_input = datetime.now().strftime('%Y-%m-%d')
        
        # Create entry file
        file_path = self.create_new_daily_entry(date_input)
        
        print(f"\n📝 Your daily journal template is ready!")
        print(f"📁 File location: {file_path}")
        print(f"\n🔧 Next steps:")
        print(f"1. Open the file in your text editor (Notepad, VS Code, etc.)")
        print(f"2. Write your market observations in your own words")
        print(f"3. Save the file when done")
        print(f"4. Run the parser to convert to AI training data")
        
        return file_path

# CLI Interface
if __name__ == "__main__":
    journal = DailyMarketJournal()
    
    while True:
        print("\n=== Daily Market Journal System ===")
        print("1. Create Today's Journal Entry")
        print("2. Create Journal Entry for Specific Date")
        print("3. Parse Existing Entry")
        print("4. Generate Weekly Summary")
        print("5. View Recent Entries")
        print("6. Exit")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == "1":
            journal.interactive_daily_entry()
        
        elif choice == "2":
            date_input = input("Enter date (YYYY-MM-DD): ").strip()
            try:
                datetime.strptime(date_input, '%Y-%m-%d')  # Validate date
                journal.create_new_daily_entry(date_input)
            except ValueError:
                print("Invalid date format. Please use YYYY-MM-DD")
        
        elif choice == "3":
            file_path = input("Enter path to journal file: ").strip()
            if os.path.exists(file_path):
                parsed = journal.parse_daily_entry(file_path)
                print(f"\nParsed entry for {parsed['date']}")
                print(f"Patterns found: {parsed['ai_training_features']['patterns_mentioned']}")
                print(f"Time references: {parsed['ai_training_features']['time_references']}")
                print(f"Price levels: {parsed['ai_training_features']['price_references']}")
            else:
                print("File not found!")
        
        elif choice == "4":
            summary = journal.generate_weekly_summary()
            print(f"\nWeekly Summary (starting {summary['week_start']}):")
            print(f"Entries found: {summary['entries_found']}")
            print(f"Total patterns: {summary['total_patterns_identified']}")
            print(f"Most mentioned: {summary['most_mentioned_patterns']}")
        
        elif choice == "5":
            # List recent entries
            journal_files = []
            for root, dirs, files in os.walk(journal.journal_dir):
                for file in files:
                    if file.endswith('.md'):
                        journal_files.append(os.path.join(root, file))
            
            journal_files.sort(reverse=True)  # Most recent first
            print(f"\nRecent journal entries:")
            for i, file_path in enumerate(journal_files[:10]):  # Show last 10
                filename = os.path.basename(file_path)
                date_str = filename.replace('.md', '')
                print(f"{i+1}. {date_str} - {file_path}")
        
        elif choice == "6":
            break
        
        else:
            print("Invalid choice. Please try again.")
